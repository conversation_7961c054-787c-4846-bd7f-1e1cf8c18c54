// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  subscription  String    @default("free")
  settings      Json      @default("{}")
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts        Account[]
  sessions        Session[]
  tutorials       Tutorial[]
  aiUsage         AIUsage[]
  analytics       Analytics[]
  permissions     UserPermission[]
  auditLogs       AuditLog[]
  grantedPermissions UserPermission[] @relation("GrantedPermissions")
  tutorialProgress TutorialProgress[]

  @@index([email])
  @@index([role])
  @@index([isActive])
  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Tutorial {
  id          String   @id @default(cuid())
  title       String
  description String?
  steps       Json
  language    String   @default("en")
  isActive    Boolean  @default(true)
  metadata    Json     @default("{}")
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creator   User        @relation(fields: [createdBy], references: [id])
  analytics Analytics[]
  progress  TutorialProgress[]

  @@map("tutorials")
}

model TutorialProgress {
  id          String    @id @default(cuid())
  tutorialId  String
  userId      String
  currentStep Int       @default(0)
  progress    Int       @default(0)
  completed   Boolean   @default(false)
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  timeSpent   Int       @default(0)
  
  tutorial    Tutorial  @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([tutorialId, userId])
  @@map("tutorial_progress")
}

model AIUsage {
  id          String        @id @default(cuid())
  userId      String
  provider    AIProvider
  model       String
  tokens      Int
  cost        Float
  requestType AIRequestType
  metadata    Json          @default("{}")
  timestamp   DateTime      @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("ai_usage")
}

model Analytics {
  id         String   @id @default(cuid())
  userId     String
  tutorialId String?
  action     String
  metadata   Json     @default("{}")
  timestamp  DateTime @default(now())

  user     User      @relation(fields: [userId], references: [id])
  tutorial Tutorial? @relation(fields: [tutorialId], references: [id])

  @@map("analytics")
}

model UserPermission {
  id         String     @id @default(cuid())
  userId     String
  permission Permission
  grantedBy  String
  grantedAt  DateTime   @default(now())
  expiresAt  DateTime?
  
  user    User @relation(fields: [userId], references: [id], onDelete: Cascade)
  granter User @relation("GrantedPermissions", fields: [grantedBy], references: [id])
  
  @@unique([userId, permission])
  @@map("user_permissions")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  resourceId String?
  oldValues Json?
  newValues Json?
  metadata  Json     @default("{}")
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())
  
  user User? @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@map("audit_logs")
}

model RolePermission {
  id         String     @id @default(cuid())
  role       UserRole
  permission Permission
  createdAt  DateTime   @default(now())
  
  @@unique([role, permission])
  @@map("role_permissions")
}

enum UserRole {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum Permission {
  // User management
  USER_READ
  USER_WRITE
  USER_DELETE
  
  // Tutorial management
  TUTORIAL_READ
  TUTORIAL_WRITE
  TUTORIAL_DELETE
  TUTORIAL_PUBLISH
  
  // Analytics
  ANALYTICS_READ
  ANALYTICS_EXPORT
  
  // AI Usage
  AI_USAGE_READ
  AI_USAGE_MANAGE
  
  // System administration
  SYSTEM_CONFIG
  AUDIT_LOG_READ
  
  // Billing
  BILLING_READ
  BILLING_WRITE
}

enum AIProvider {
  openai
  anthropic
  google
  openrouter
  groq
}

enum AIRequestType {
  explanation
  tutorial_generation
  chat
  translation
  summarization
}
