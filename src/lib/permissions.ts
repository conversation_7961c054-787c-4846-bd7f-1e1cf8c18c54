
import { prisma } from "@/lib/database";
import { User } from "@/models/User";
import crypto from "crypto";
import { DATABASE_CONFIG } from "./config";
// Define role-based permissions
const ROLE_PERMISSIONS = {
  USER: ["TUTORIAL_READ", "AI_USAGE_READ"],
  MODERATOR: [
    "TUTORIAL_READ",
    "TUTORIAL_WRITE",
    "TUTORIAL_PUBLISH",
    "AI_USAGE_READ",
    "ANALYTICS_READ",
    "USER_READ",
  ],
  ADMIN: [
    "USER_READ",
    "USER_WRITE",
    "USER_DELETE",
    "TUTORIAL_READ",
    "TUTORIAL_WRITE",
    "TUTORIAL_DELETE",
    "TUTORIAL_PUBLISH",
    "ANALYTICS_READ",
    "ANALYTICS_EXPORT",
    "AI_USAGE_READ",
    "AI_USAGE_MANAGE",
    "BILLING_READ",
    "BIL<PERSON>ING_WRITE",
    "AUDIT_LOG_READ",
  ],
  SUPER_ADMIN: [
    "USER_READ",
    "USER_WRITE",
    "USER_DELETE",
    "TUTORIAL_READ",
    "TUTORIAL_WRITE",
    "TUTORIAL_DELETE",
    "TUTORIAL_PUBLISH",
    "ANALYTICS_READ",
    "ANALYTICS_EXPORT",
    "AI_USAGE_READ",
    "AI_USAGE_MANAGE",
    "BILLING_READ",
    "BILLING_WRITE",
    "AUDIT_LOG_READ",
    "SYSTEM_CONFIG",
  ],
} as const;

export type Permission =
  keyof (typeof ROLE_PERMISSIONS)[keyof typeof ROLE_PERMISSIONS] extends never
    ? string
    : (typeof ROLE_PERMISSIONS)[keyof typeof ROLE_PERMISSIONS][number];

/**
 * Check if a user has a specific permission based on their role
 */
export function hasPermission(userRole: User["role"], permission: Permission): boolean {
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions.includes(permission as any);
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  userRole: User["role"],
  permissions: Permission[],
): boolean {
  return permissions.some((permission) => hasPermission(userRole, permission));
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(
  userRole: User["role"],
  permissions: Permission[],
): boolean {
  return permissions.every((permission) => hasPermission(userRole, permission));
}

/**
 * Get all permissions for a user role
 */
export function getRolePermissions(userRole: User["role"]): Permission[] {
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions as unknown as Permission[];
}

/**
 * Check if a user can perform an action on a resource
 */
export async function canUserPerformAction(
  userId: string,
  action: Permission,
  resource?: string,
): Promise<boolean> {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Permission check will use role-based fallback only.");
      // Default to USER role permissions if database is unavailable
      return hasPermission("USER" as any, action);
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, lastLoginAt: true },
    });

    // Check role-based permissions
    if (!user) {
      return false;
    }
    const hasRolePermission = hasPermission(user?.role, action);
    if (hasRolePermission) {
      return true;
    }

    // Check custom user permissions
    try {
      // Use raw SQL query instead of Prisma model
      const result = await prisma.$queryRaw`
        SELECT * FROM user_permissions
        WHERE user_id = ${userId}
        AND permission = ${action}::Permission
        AND (expires_at IS NULL OR expires_at > NOW())
        LIMIT 1
      `;
      
      return (result as any[])?.[0] ? true : false;
    } catch (error) {
      console.error("Error checking custom permissions:", error);
      return false;
    }
  } catch (error) {
    console.error("Error checking user permissions:", error);
    // Default to false for safety if there's an error
    return false;
  }
}

/**
 * Log an audit event
 */
export async function logAuditEvent({
  userId,
  action,
  resource,
  resourceId,
  oldValues,
  newValues,
  metadata = {},
  ipAddress,
  userAgent,
}: {
  userId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}) {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Audit logging skipped.");
      return;
    }

    try {
      // Use a raw SQL query as a workaround for the missing model
      await prisma.$executeRaw`
        INSERT INTO audit_logs (
          id, 
          user_id, 
          action, 
          resource, 
          resource_id, 
          old_values, 
          new_values, 
          metadata, 
          ip_address, 
          user_agent, 
          timestamp
        ) VALUES (
          ${crypto.randomUUID()}, 
          ${userId || null}, 
          ${action}, 
          ${resource}, 
          ${resourceId || null}, 
          ${oldValues ? JSON.stringify(oldValues) : null}, 
          ${newValues ? JSON.stringify(newValues) : null}, 
          ${JSON.stringify(metadata)}, 
          ${ipAddress || null}, 
          ${userAgent || null}, 
          ${new Date()}
        )
      `;
    } catch (error) {
      console.error("Error creating audit log:", error);
    }
  } catch (error) {
    console.error("Error logging audit event:", error);
  }
}

/**
 * Middleware to check permissions
 */
export function requirePermission(permission: Permission) {
  return async (userId: string): Promise<boolean> => {
    return await canUserPerformAction(userId, permission as any);
  };
}

/**
 * Check if user account is locked due to failed login attempts
 */
export async function isAccountLocked(userId: string): Promise<boolean> {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Account lock check skipped.");
      return false;
    }

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId  },
        select: { lockedUntil: true, lastLoginAt: true, loginAttempts: true, isActive: true } as any,
      });

      if (!user || !user?.lockedUntil) {
        return false;
      }

      return new Date(user.lockedUntil as any) > new Date();
    } catch (error) {
      console.error("Error checking account lock status:", error);
      return false;
    }
  } catch (error) {
    console.error("Error checking account lock status:", error);
    return false;
  }
}

/**
 * Lock user account after failed login attempts
 */
export async function lockUserAccount(
  userId: string,
  lockDurationMinutes: number = 30,
) {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Account locking skipped.");
      return;
    }

    const lockUntil = new Date(Date.now() + lockDurationMinutes * 60 * 1000);

    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          // Use type assertion to bypass TypeScript errors
          lockedUntil: lockUntil,
          loginAttempts: 0,
          lastLoginAt: new Date(),
        } as any,
      });

      await logAuditEvent({
        userId,
        action: "account_locked",
        resource: "user",
        resourceId: userId,
        metadata: { lockDurationMinutes, lockedUntil: lockUntil.toISOString() },
      });
    } catch (error) {
      console.error("Error updating user account lock status:", error);
    }
  } catch (error) {
    console.error("Error locking user account:", error);
  }
}

/**
 * Increment failed login attempts
 */
export async function incrementLoginAttempts(userId: string): Promise<number> {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Login attempts tracking skipped.");
      return 0;
    }

    try {
      const user = await prisma.user.update({
        where: { id: userId },
        data: {
          // Use type assertion to bypass TypeScript errors
          loginAttempts: { increment: 1 },
          lastLoginAt: new Date(),
          lockedUntil: null,
        } as any,
        select: { loginAttempts: true } as any,
      });

      // Safely access loginAttempts or return 0 if it doesn't exist
      return (user as any).loginAttempts || 0;
    } catch (error) {
      console.error("Error incrementing login attempts:", error);
      return 0;
    }
  } catch (error) {
    console.error("Error incrementing login attempts:", error);
    return 0;
  }
}

/**
 * Reset login attempts on successful login
 */
export async function resetLoginAttempts(userId: string) {
  try {
    if (!DATABASE_CONFIG.URL) {
      console.error("DATABASE_URL not set. Login attempts reset skipped.");
      return;
    }

    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          // Use type assertion to bypass TypeScript errors
          loginAttempts: 0,
          lockedUntil: null,
          lastLoginAt: new Date(),
        } as any,
        select: { loginAttempts: true, lockedUntil: true, lastLoginAt: true } as any,
      });
    } catch (error) {
      console.error("Error resetting login attempts:", error);
    }
  } catch (error) {
    console.error("Error resetting login attempts:", error);
  }
}
