/**
 * AG-UI Server Implementation for TutorAI
 * Handles AI provider integration and event streaming
 */

import type { AgentEvent } from '@/lib/ag-ui-types';

// AI Provider types for better type safety
type AIProviderName = 'openai' | 'anthropic' | 'groq' | 'openrouter';

export interface AIProvider {
  name: 'openai' | 'anthropic' | 'groq' | 'openrouter';
  apiKey: string;
  model: string;
  baseUrl?: string;
}

export interface TutorialContext {
  pageUrl: string;
  pageTitle?: string;
  userQuery?: string;
  domElements?: Array<{
    selector: string;
    text: string;
    type: string;
  }>;
}

export interface AgUIServerConfig {
  aiProviders: AIProvider[];
  defaultProvider: 'openai' | 'anthropic' | 'groq' | 'openrouter';
  maxTokens: number;
  temperature: number;
}

export class TutorAIAgentServer {
  private config: AgUIServerConfig;
  private openaiClient?: any;
  private anthropicClient?: any;
  private groqClient?: any;
  private eventListeners: ((event: AgentEvent) => void)[] = [];

  constructor(config: AgUIServerConfig) {
    this.config = config;
    this.initializeProviders();
  }

  /**
   * Initialize AI provider clients with dynamic imports and fallback handling
   */
  private async initializeProviders(): Promise<void> {
    // Initialize OpenAI client
    const openaiProvider = this.config.aiProviders.find(p => p.name === 'openai');
    if (openaiProvider) {
      this.openaiClient = await this.createOpenAIClient(openaiProvider);
    }

    // Initialize Anthropic client
    const anthropicProvider = this.config.aiProviders.find(p => p.name === 'anthropic');
    if (anthropicProvider) {
      this.anthropicClient = await this.createAnthropicClient(anthropicProvider);
    }

    // Initialize Groq client
    const groqProvider = this.config.aiProviders.find(p => p.name === 'groq');
    if (groqProvider) {
      this.groqClient = await this.createGroqClient(groqProvider);
    }
  }

  /**
   * Create OpenAI client with fallback
   */
  private async createOpenAIClient(provider: AIProvider): Promise<any> {
    try {
      // Try to dynamically import OpenAI
      const OpenAI = await this.dynamicImport('openai');
      if (OpenAI) {
        return new OpenAI.default({
          apiKey: provider.apiKey,
          baseURL: provider.baseUrl
        });
      }
    } catch (error) {
      console.warn('OpenAI SDK not available, using fallback implementation');
    }

    // Return fallback implementation
    return this.createFallbackClient(provider, 'openai');
  }

  /**
   * Create Anthropic client with fallback
   */
  private async createAnthropicClient(provider: AIProvider): Promise<any> {
    try {
      const Anthropic = await this.dynamicImport('@anthropic-ai/sdk');
      if (Anthropic) {
        return new Anthropic.default({
          apiKey: provider.apiKey
        });
      }
    } catch (error) {
      console.warn('Anthropic SDK not available, using fallback implementation');
    }

    return this.createFallbackClient(provider, 'anthropic');
  }

  /**
   * Create Groq client with fallback
   */
  private async createGroqClient(provider: AIProvider): Promise<any> {
    try {
      const Groq = await this.dynamicImport('groq-sdk');
      if (Groq) {
        return new Groq.default({
          apiKey: provider.apiKey
        });
      }
    } catch (error) {
      console.warn('Groq SDK not available, using fallback implementation');
    }

    return this.createFallbackClient(provider, 'groq');
  }

  /**
   * Dynamic import with error handling
   */
  private async dynamicImport(packageName: string): Promise<any> {
    try {
      return await import(packageName);
    } catch (error) {
      return null;
    }
  }

  /**
   * Create fallback client for HTTP-based API calls
   */
  private createFallbackClient(provider: AIProvider, providerType: string): any {
    const baseClient = {
      provider: providerType,
      apiKey: provider.apiKey,
      baseURL: provider.baseUrl
    };

    if (providerType === 'openai') {
      return {
        ...baseClient,
        chat: {
          completions: {
            create: async (params: any) => {
              return this.makeHttpRequest('https://api.openai.com/v1/chat/completions', {
                ...params,
                headers: {
                  'Authorization': `Bearer ${provider.apiKey}`,
                  'Content-Type': 'application/json'
                }
              });
            }
          }
        }
      };
    }

    if (providerType === 'anthropic') {
      return {
        ...baseClient,
        messages: {
          create: async (params: any) => {
            return this.makeHttpRequest('https://api.anthropic.com/v1/messages', {
              ...params,
              headers: {
                'x-api-key': provider.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
              }
            });
          }
        }
      };
    }

    if (providerType === 'groq') {
      return {
        ...baseClient,
        chat: {
          completions: {
            create: async (params: any) => {
              return this.makeHttpRequest('https://api.groq.com/openai/v1/chat/completions', {
                ...params,
                headers: {
                  'Authorization': `Bearer ${provider.apiKey}`,
                  'Content-Type': 'application/json'
                }
              });
            }
          }
        }
      };
    }

    return baseClient;
  }

  /**
   * Make HTTP request for fallback implementations
   */
  private async makeHttpRequest(url: string, options: any): Promise<any> {
    try {
      // Try to use fetch (available in Node.js 18+)
      const response = await fetch(url, {
        method: 'POST',
        headers: options.headers,
        body: JSON.stringify(options)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`HTTP request failed for ${url}:`, error);
      throw error;
    }
  }

  /**
   * Add event listener for AG-UI events
   */
  onEvent(listener: (event: AgentEvent) => void): void {
    this.eventListeners.push(listener);
  }

  /**
   * Emit AG-UI event to all listeners
   */
  private emitEvent(event: AgentEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in AG-UI event listener:', error);
      }
    });
  }

  /**
   * Generate tutorial steps for a webpage
   */
  async generateTutorial(context: TutorialContext, runId: string, threadId: string): Promise<void> {
    // Emit run started event
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    } as AgentEvent);

    try {
      // Step 1: Analyze page structure
      this.emitEvent({
        type: 'StepStarted',
        stepName: 'analyze_page',
        timestamp: new Date().toISOString()
      } as AgentEvent);

      const pageAnalysis = await this.analyzePageStructure(context);

      this.emitEvent({
        type: 'StepFinished',
        stepName: 'analyze_page',
        timestamp: new Date().toISOString()
      } as AgentEvent);

      // Step 2: Generate tutorial content
      this.emitEvent({
        type: 'StepStarted',
        stepName: 'generate_content',
        timestamp: new Date().toISOString()
      } as AgentEvent);

      await this.generateTutorialContent(context, pageAnalysis, runId, threadId);

      this.emitEvent({
        type: 'StepFinished',
        stepName: 'generate_content',
        timestamp: new Date().toISOString()
      } as AgentEvent);

      // Emit run finished event
      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      } as AgentEvent);

    } catch (error) {
      console.error('Error generating tutorial:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'TUTORIAL_GENERATION_ERROR',
        timestamp: new Date().toISOString()
      } as AgentEvent);
    }
  }

  /**
   * Analyze page structure using AI with multiple provider fallback
   */
  private async analyzePageStructure(context: TutorialContext): Promise<any> {
    const prompt = `Analyze this webpage structure and identify key interactive elements:

URL: ${context.pageUrl}
Title: ${context.pageTitle || 'Unknown'}
User Query: ${context.userQuery || 'General tutorial'}

DOM Elements:
${context.domElements?.map(el => `- ${el.type}: "${el.text}" (${el.selector})`).join('\n') || 'No elements provided'}

Please identify the most important elements for a tutorial and suggest a logical learning sequence.`;

    // Try OpenAI first
    if (this.openaiClient) {
      try {
        const response = await this.openaiClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature
        });

        return response.choices[0]?.message?.content;
      } catch (error) {
        console.warn('OpenAI request failed, trying fallback:', error);
      }
    }

    // Try Groq as fallback
    if (this.groqClient) {
      try {
        const response = await this.groqClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'groq')?.model || 'mixtral-8x7b-32768',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature
        });

        return response.choices[0]?.message?.content;
      } catch (error) {
        console.warn('Groq request failed, trying Anthropic:', error);
      }
    }

    // Try Anthropic as final fallback
    if (this.anthropicClient) {
      try {
        const response = await this.anthropicClient.messages.create({
          model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          messages: [{ role: 'user', content: prompt }]
        });

        return response.content[0]?.type === 'text' ? response.content[0].text : '';
      } catch (error) {
        console.warn('Anthropic request failed:', error);
      }
    }

    throw new Error('No AI provider available or all providers failed');
  }

  /**
   * Generate tutorial content with streaming
   */
  private async generateTutorialContent(
    context: TutorialContext,
    pageAnalysis: string,
    runId: string,
    threadId: string
  ): Promise<void> {
    const messageId = `msg_${Date.now()}`;

    // Start text message
    this.emitEvent({
      type: 'TextMessageStart',
      messageId,
      role: 'assistant',
      timestamp: new Date().toISOString()
    } as AgentEvent);

    const prompt = `Based on the page analysis, create a step-by-step tutorial:

${pageAnalysis}

Create a comprehensive tutorial that guides the user through the most important features of this webpage. Format your response as a structured tutorial with clear steps.`;

    try {
      if (this.openaiClient) {
        const stream = await this.openaiClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          stream: true
        });

        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content;
          if (content) {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: content,
              timestamp: new Date().toISOString()
            } as AgentEvent);
          }
        }
      } else if (this.anthropicClient) {
        const stream = await this.anthropicClient.messages.create({
          model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          messages: [{ role: 'user', content: prompt }],
          stream: true
        });

        for await (const chunk of stream) {
          if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: chunk.delta.text,
              timestamp: new Date().toISOString()
            } as AgentEvent);
          }
        }
      }

      // End text message
      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      } as AgentEvent);

    } catch (error) {
      console.error('Error streaming tutorial content:', error);
      throw error;
    }
  }

  /**
   * Explain a specific DOM element
   */
  async explainElement(
    selector: string,
    question: string | undefined,
    pageContext: string,
    runId: string,
    threadId: string
  ): Promise<void> {
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    } as AgentEvent);

    try {
      const messageId = `msg_${Date.now()}`;

      // Start text message
      this.emitEvent({
        type: 'TextMessageStart',
        messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      } as AgentEvent);

      const prompt = question
        ? `User is asking about element "${selector}" on page ${pageContext}: "${question}". Please provide a helpful explanation.`
        : `Please explain what the element "${selector}" does on page ${pageContext} and how the user can interact with it.`;

      // Stream explanation
      await this.streamAIResponse(prompt, messageId);

      // End text message
      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      } as AgentEvent);

      // Emit tool call for highlighting
      await this.emitHighlightTool(selector, runId, threadId);

      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      } as AgentEvent);

    } catch (error) {
      console.error('Error explaining element:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'ELEMENT_EXPLANATION_ERROR',
        timestamp: new Date().toISOString()
      } as AgentEvent);
    }
  }

  /**
   * Stream AI response content with multiple provider support
   */
  private async streamAIResponse(prompt: string, messageId: string): Promise<void> {
    // Try OpenAI first
    if (this.openaiClient) {
      try {
        const stream = await this.openaiClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          stream: true
        });

        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content;
          if (content) {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: content,
              timestamp: new Date().toISOString()
            } as AgentEvent);
          }
        }
        return;
      } catch (error) {
        console.warn('OpenAI streaming failed, trying fallback:', error);
      }
    }

    // Try Groq as fallback
    if (this.groqClient) {
      try {
        const stream = await this.groqClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'groq')?.model || 'mixtral-8x7b-32768',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          stream: true
        });

        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content;
          if (content) {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: content,
              timestamp: new Date().toISOString()
            } as AgentEvent);
          }
        }
        return;
      } catch (error) {
        console.warn('Groq streaming failed, trying Anthropic:', error);
      }
    }

    // Try Anthropic as final fallback
    if (this.anthropicClient) {
      try {
        const stream = await this.anthropicClient.messages.create({
          model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          messages: [{ role: 'user', content: prompt }],
          stream: true
        });

        for await (const chunk of stream) {
          if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: chunk.delta.text,
              timestamp: new Date().toISOString()
            } as AgentEvent);
          }
        }
        return;
      } catch (error) {
        console.warn('Anthropic streaming failed:', error);
      }
    }

    throw new Error('No AI provider available for streaming');
  }

  /**
   * Emit tool call for element highlighting
   */
  private async emitHighlightTool(selector: string, runId: string, threadId: string): Promise<void> {
    const toolCallId = `tool_${Date.now()}`;

    // Start tool call
    this.emitEvent({
      type: 'ToolCallStart',
      toolCallId,
      toolCallName: 'highlight_element',
      timestamp: new Date().toISOString()
    } as AgentEvent);

    // Stream tool arguments
    const args = JSON.stringify({ selector, style: { borderColor: '#3b82f6', pulseAnimation: true } });
    this.emitEvent({
      type: 'ToolCallArgs',
      toolCallId,
      delta: args,
      timestamp: new Date().toISOString()
    } as AgentEvent);

    // End tool call
    this.emitEvent({
      type: 'ToolCallEnd',
      toolCallId,
      timestamp: new Date().toISOString()
    } as AgentEvent);

    // Tool result
    this.emitEvent({
      type: 'ToolCallResult',
      messageId: `msg_${Date.now()}`,
      toolCallId,
      content: `Element ${selector} highlighted successfully`,
      role: 'tool',
      timestamp: new Date().toISOString()
    } as AgentEvent);
  }

  /**
   * Update tutorial state
   */
  updateState(state: any): void {
    this.emitEvent({
      type: 'StateSnapshot',
      snapshot: state,
      timestamp: new Date().toISOString()
    } as AgentEvent);
  }

  /**
   * Apply state delta
   */
  applyStateDelta(operations: any[]): void {
    this.emitEvent({
      type: 'StateDelta',
      delta: operations,
      timestamp: new Date().toISOString()
    } as AgentEvent);
  }
}

// Default server configuration
export const defaultAgUIServerConfig: AgUIServerConfig = {
  aiProviders: [
    {
      name: 'openai' as const,
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4-turbo-preview'
    },
    {
      name: 'anthropic' as const,
      apiKey: process.env.ANTHROPIC_API_KEY || '',
      model: 'claude-3-sonnet-20240229'
    }
  ].filter(provider => provider.apiKey), // Only include providers with API keys
  defaultProvider: 'openai' as const,
  maxTokens: 2000,
  temperature: 0.7
};

// Singleton instance
let agUIServer: TutorAIAgentServer | null = null;

export function getAgUIServer(): TutorAIAgentServer {
  if (!agUIServer) {
    agUIServer = new TutorAIAgentServer(defaultAgUIServerConfig);
  }
  return agUIServer;
}
