/**
 * Centralized configuration settings with comprehensive validation
 * All environment variables and configuration settings should be accessed through this file
 */

import { z } from "zod";

// Environment validation schemas
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url().optional(),
  DB_HOST: z.string().default("localhost"),
  DB_PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default("5432"),
  DB_USER: z.string().default("postgres"),
  DB_PASSWORD: z.string().min(1, "Database password is required"),
  DB_NAME: z.string().default("tutorial_db"),
  DB_SCHEMA: z.string().default("public"),

  // Authentication
  NEXTAUTH_URL: z.string().url("NEXTAUTH_URL must be a valid URL"),
  NEXTAUTH_SECRET: z
    .string()
    .min(32, "NEXTAUTH_SECRET must be at least 32 characters"),

  // OAuth (optional)
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),

  // AI Providers (at least one required)
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GROQ_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),
  GOOGLE_AI_API_KEY: z.string().optional(),

  // Voice synthesis (optional)
  ELEVENLABS_API_KEY: z.string().optional(),

  // AG-UI
  AG_UI_API_KEY: z.string().optional(),
  NEXT_PUBLIC_AG_UI_TRANSPORT: z.enum(["sse", "websocket"]).default("sse"),

  // Application
  NODE_ENV: z
    .enum(["development", "staging", "production"])
    .default("development"),
  PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default("3000"),
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  API_URL: z.string().url().optional(),

  // Email (for password reset)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_FROM: z.string().email().optional(),
});

// Validate environment variables
const validateEnv = () => {
  try {
    const env = envSchema.parse(process.env);

    // Additional validation logic
    if (!env.DATABASE_URL && (!env.DB_HOST || !env.DB_PASSWORD)) {
      throw new Error(
        "Either DATABASE_URL or DB_HOST and DB_PASSWORD must be provided",
      );
    }

    // Check if at least one AI provider is configured
    const aiProviders = [
      env.OPENAI_API_KEY,
      env.ANTHROPIC_API_KEY,
      env.GROQ_API_KEY,
      env.OPENROUTER_API_KEY,
      env.GOOGLE_AI_API_KEY,
    ].filter(Boolean);

    if (aiProviders.length === 0 && env.NODE_ENV === "production") {
      console.warn(
        "Warning: No AI providers configured. Some features may not work.",
      );
    }

    return env;
  } catch (error) {
    console.error("Environment validation failed:", error);
    if (process.env.NODE_ENV === "production") {
      process.exit(1);
    }
    throw error;
  }
};

// Validate and export environment
const env = validateEnv();

// Database configuration
export const DATABASE_CONFIG = {
  URL: env.DATABASE_URL,
  HOST: env.DB_HOST,
  PORT: env.DB_PORT,
  USER: env.DB_USER,
  PASSWORD: env.DB_PASSWORD,
  NAME: env.DB_NAME,
  SCHEMA: env.DB_SCHEMA,
  CONNECTION_POOL: {
    min: env.NODE_ENV === "production" ? 5 : 2,
    max: env.NODE_ENV === "production" ? 20 : 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  },
};

// Auth configuration
export const AUTH_CONFIG = {
  NEXTAUTH_URL: env.NEXTAUTH_URL,
  NEXTAUTH_SECRET: env.NEXTAUTH_SECRET,
  GOOGLE_CLIENT_ID: env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: env.GOOGLE_CLIENT_SECRET,
  session: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 30 * 60 * 1000, // 30 minutes
    passwordResetExpiry: 60 * 60 * 1000, // 1 hour
  },
};

// Application settings
export const APP_CONFIG = {
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  API_URL: env.API_URL || `${env.NEXTAUTH_URL}/api`,
  PUBLIC_URL: env.NEXT_PUBLIC_APP_URL || env.NEXTAUTH_URL,
};

// AI Provider configuration
export const AI_CONFIG = {
  OPENAI_API_KEY: env.OPENAI_API_KEY,
  ANTHROPIC_API_KEY: env.ANTHROPIC_API_KEY,
  GROQ_API_KEY: env.GROQ_API_KEY,
  OPENROUTER_API_KEY: env.OPENROUTER_API_KEY,
  GOOGLE_AI_API_KEY: env.GOOGLE_AI_API_KEY,
};

// Email configuration
export const EMAIL_CONFIG = {
  SMTP_HOST: env.SMTP_HOST,
  SMTP_PORT: env.SMTP_PORT,
  SMTP_USER: env.SMTP_USER,
  SMTP_PASSWORD: env.SMTP_PASSWORD,
  SMTP_FROM: env.SMTP_FROM,
};

// Rate limiting configuration
export const RATE_LIMIT_CONFIG = {
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
  },
  api: {
    windowMs: 60 * 1000, // 1 minute
    max: 100, // 100 requests per minute
  },
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 attempts per hour
  },
};

// Helper function to build DATABASE_URL from individual components
export function buildDatabaseUrl(): string {
  if (process.env.DATABASE_URL) {
    return process.env.DATABASE_URL;
  }

  return `postgresql://${DATABASE_CONFIG.USER}:${DATABASE_CONFIG.PASSWORD}@${DATABASE_CONFIG.HOST}:${DATABASE_CONFIG.PORT}/${DATABASE_CONFIG.NAME}?schema=${DATABASE_CONFIG.SCHEMA}`;
}
