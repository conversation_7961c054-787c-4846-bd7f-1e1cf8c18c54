/**
 * AG-UI Fallback Implementation
 * Works without external AG-UI packages using local types and OpenAI/Anthropic directly
 */

import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import type { AgentEvent } from '@/lib/ag-ui-types';

export interface AIProvider {
  name: 'openai' | 'anthropic' | 'google';
  apiKey: string;
  model: string;
  baseUrl?: string;
}

export interface TutorialContext {
  pageUrl: string;
  pageTitle?: string;
  userQuery?: string;
  domElements?: Array<{
    selector: string;
    text: string;
    type: string;
  }>;
}

export interface AgUIServerConfig {
  aiProviders: AIProvider[];
  defaultProvider: 'openai' | 'anthropic' | 'google';
  maxTokens: number;
  temperature: number;
}

export class TutorAIAgentServerFallback {
  private config: AgUIServerConfig;
  private openaiClient?: OpenAI;
  private anthropicClient?: Anthropic;
  private eventListeners: ((event: AgentEvent) => void)[] = [];

  constructor(config: AgUIServerConfig) {
    this.config = config;
    this.initializeProviders();
  }

  private initializeProviders(): void {
    const openaiProvider = this.config.aiProviders.find(p => p.name === 'openai');
    if (openaiProvider?.apiKey) {
      try {
        this.openaiClient = new OpenAI({
          apiKey: openaiProvider.apiKey,
          baseURL: openaiProvider.baseUrl
        });
      } catch (error) {
        console.warn('OpenAI client initialization failed:', error);
      }
    }

    const anthropicProvider = this.config.aiProviders.find(p => p.name === 'anthropic');
    if (anthropicProvider?.apiKey) {
      try {
        this.anthropicClient = new Anthropic({
          apiKey: anthropicProvider.apiKey
        });
      } catch (error) {
        console.warn('Anthropic client initialization failed:', error);
      }
    }
  }

  onEvent(listener: (event: AgentEvent) => void): void {
    this.eventListeners.push(listener);
  }

  private emitEvent(event: AgentEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in AG-UI event listener:', error);
      }
    });
  }

  async explainElement(
    selector: string, 
    question: string | undefined, 
    pageContext: string,
    runId: string,
    threadId: string
  ): Promise<void> {
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    });

    try {
      const messageId = `msg_${Date.now()}`;

      // Start text message
      this.emitEvent({
        type: 'TextMessageStart',
        messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      });

      const prompt = question 
        ? `User is asking about element "${selector}" on page ${pageContext}: "${question}". Please provide a helpful explanation.`
        : `Please explain what the element "${selector}" does on page ${pageContext} and how the user can interact with it.`;

      // Stream explanation
      await this.streamAIResponse(prompt, messageId);

      // End text message
      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      });

      // Emit tool call for highlighting
      await this.emitHighlightTool(selector, runId, threadId);

      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error explaining element:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'ELEMENT_EXPLANATION_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }

  private async streamAIResponse(prompt: string, messageId: string): Promise<void> {
    if (this.openaiClient) {
      try {
        const stream = await this.openaiClient.chat.completions.create({
          model: this.config.aiProviders.find(p => p.name === 'openai')?.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          stream: true
        });

        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content;
          if (content) {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: content,
              timestamp: new Date().toISOString()
            });
          }
        }
        return;
      } catch (error) {
        console.error('OpenAI streaming error:', error);
      }
    }

    if (this.anthropicClient) {
      try {
        const stream = await this.anthropicClient.messages.create({
          model: this.config.aiProviders.find(p => p.name === 'anthropic')?.model || 'claude-3-sonnet-20240229',
          max_tokens: this.config.maxTokens,
          temperature: this.config.temperature,
          messages: [{ role: 'user', content: prompt }],
          stream: true
        });

        for await (const chunk of stream) {
          if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
            this.emitEvent({
              type: 'TextMessageContent',
              messageId,
              delta: chunk.delta.text,
              timestamp: new Date().toISOString()
            });
          }
        }
        return;
      } catch (error) {
        console.error('Anthropic streaming error:', error);
      }
    }

    // Fallback response if no AI providers available
    const fallbackResponse = "I can help explain elements on this webpage. Please ensure your AI provider API keys are configured correctly.";
    this.emitEvent({
      type: 'TextMessageContent',
      messageId,
      delta: fallbackResponse,
      timestamp: new Date().toISOString()
    });
  }

  private async emitHighlightTool(selector: string, runId: string, threadId: string): Promise<void> {
    const toolCallId = `tool_${Date.now()}`;

    // Start tool call
    this.emitEvent({
      type: 'ToolCallStart',
      toolCallId,
      toolCallName: 'highlight_element',
      timestamp: new Date().toISOString()
    });

    // Stream tool arguments
    const args = JSON.stringify({ selector, style: { borderColor: '#3b82f6', pulseAnimation: true } });
    this.emitEvent({
      type: 'ToolCallArgs',
      toolCallId,
      delta: args,
      timestamp: new Date().toISOString()
    });

    // End tool call
    this.emitEvent({
      type: 'ToolCallEnd',
      toolCallId,
      timestamp: new Date().toISOString()
    });

    // Tool result
    this.emitEvent({
      type: 'ToolCallResult',
      messageId: `msg_${Date.now()}`,
      toolCallId,
      content: `Element ${selector} highlighted successfully`,
      role: 'tool',
      timestamp: new Date().toISOString()
    });
  }

  async generateTutorial(context: TutorialContext, runId: string, threadId: string): Promise<void> {
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    });

    try {
      const messageId = `msg_${Date.now()}`;

      this.emitEvent({
        type: 'TextMessageStart',
        messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      });

      const prompt = `Create a tutorial for this webpage:
URL: ${context.pageUrl}
Title: ${context.pageTitle || 'Unknown'}
User Query: ${context.userQuery || 'General tutorial'}

Please provide step-by-step guidance for using this webpage effectively.`;

      await this.streamAIResponse(prompt, messageId);

      this.emitEvent({
        type: 'TextMessageEnd',
        messageId,
        timestamp: new Date().toISOString()
      });

      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error generating tutorial:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'TUTORIAL_GENERATION_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }

  updateState(state: any): void {
    this.emitEvent({
      type: 'StateSnapshot',
      snapshot: state,
      timestamp: new Date().toISOString()
    });
  }

  applyStateDelta(operations: any[]): void {
    this.emitEvent({
      type: 'StateDelta',
      delta: operations,
      timestamp: new Date().toISOString()
    });
  }
}

// Default server configuration
export const defaultAgUIServerConfig: AgUIServerConfig = {
  aiProviders: [
    {
      name: 'openai',
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4-turbo-preview'
    },
    {
      name: 'anthropic',
      apiKey: process.env.ANTHROPIC_API_KEY || '',
      model: 'claude-3-sonnet-20240229'
    }
  ].filter(provider => provider.apiKey), // Only include providers with API keys
  defaultProvider: 'openai',
  maxTokens: 2000,
  temperature: 0.7
};

// Singleton instance
let agUIServerFallback: TutorAIAgentServerFallback | null = null;

export function getAgUIServerFallback(): TutorAIAgentServerFallback {
  if (!agUIServerFallback) {
    agUIServerFallback = new TutorAIAgentServerFallback(defaultAgUIServerConfig);
  }
  return agUIServerFallback;
}
