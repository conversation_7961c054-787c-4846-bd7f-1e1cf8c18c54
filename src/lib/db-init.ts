import { prisma } from './database';
import { defaultUserSettings } from '@/models/User';
import crypto from 'crypto';
import { DATABASE_CONFIG } from './config';

/**
 * Initialize the database with required data
 * This script can be used to seed the database with initial data
 */
export async function initializeDatabase() {
  try {
    console.log('Initializing database...');

    // Check if admin user exists
    const adminExists = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    // Create admin user if none exists
    if (!adminExists) {
      console.log('Creating admin user...');
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'ADMIN',
          settings: defaultUserSettings as any,
          isActive: true,
          subscription: 'premium',
          // In a real app, you would hash this password
          password: 'changeme123', 
        }
      });
      console.log('Admin user created successfully');
    }

    // Initialize role permissions
    const rolePermissionResult = await prisma.$queryRaw`SELECT COUNT(*) FROM user_permissions`;
    const rolePermissionCount = Number((rolePermissionResult as any[])[0].count || 0);
    
    if (rolePermissionCount === 0) {
      console.log('Initializing role permissions...');
      
      // Define role permissions mapping
      const rolePermissions = [
        // USER role permissions
        { userId: 'USER', permission: 'TUTORIAL_READ' },
        { userId: 'USER', permission: 'AI_USAGE_READ' },
        
        // MODERATOR role permissions
        { userId: 'MODERATOR', permission: 'TUTORIAL_READ' },
        { userId: 'MODERATOR', permission: 'TUTORIAL_WRITE' },
        { userId: 'MODERATOR', permission: 'TUTORIAL_PUBLISH' },
        { userId: 'MODERATOR', permission: 'AI_USAGE_READ' },
        { userId: 'MODERATOR', permission: 'ANALYTICS_READ' },
        { userId: 'MODERATOR', permission: 'USER_READ' },
        
        // ADMIN role permissions (add all admin permissions)
        { userId: 'ADMIN', permission: 'USER_READ' },
        { userId: 'ADMIN', permission: 'USER_WRITE' },
        { userId: 'ADMIN', permission: 'USER_DELETE' },
        // ... add other admin permissions
        
        // SUPER_ADMIN role permissions (add all permissions)
        { userId: 'SUPER_ADMIN', permission: 'SYSTEM_CONFIG' },
        // ... add other super admin permissions
      ];
      
      // Create role permissions
      for (const rp of rolePermissions) {
        await prisma.$executeRaw`
          INSERT INTO user_permissions (id, user_id, permission, granted_by, granted_at)
          VALUES (
            ${crypto.randomUUID()}, 
            ${rp.userId}, 
            ${rp.permission}::Permission, 
            'system', 
            NOW()
          )
        `;
      }
      
      console.log('Role permissions initialized successfully');
    }

    console.log('Database initialization completed successfully');
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
}

// Run the initialization if this file is executed directly
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('Database initialization script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database initialization failed:', error);
      process.exit(1);
    });
} 