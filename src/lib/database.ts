import { PrismaClient } from "@prisma/client";
import { DATABASE_CONFIG, buildDatabaseUrl } from "./config";

// Validate database configuration
if (
  !DATABASE_CONFIG.URL &&
  (!DATABASE_CONFIG.HOST || !DATABASE_CONFIG.PASSWORD)
) {
  const error = new Error(
    "Database configuration is incomplete. Please check your environment variables.",
  );
  console.error(error.message);
  if (process.env.NODE_ENV === "production") {
    throw error;
  }
}

// Define global type for Prisma client
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Create Prisma client with production-ready configuration
const createPrismaClient = () => {
  return new PrismaClient({
    log: [
      { emit: "event", level: "query" },
      { emit: "event", level: "error" },
      { emit: "event", level: "info" },
      { emit: "event", level: "warn" },
    ],
    datasources: {
      db: {
        url: buildDatabaseUrl(),
      },
    },
  });
};

// Initialize Prisma client
const prismaInstance = createPrismaClient();

// Add event listeners for logging and monitoring
prismaInstance.$on("query", (e) => {
  if (process.env.NODE_ENV === "development") {
    console.log("Query: " + e.query);
    console.log("Duration: " + e.duration + "ms");
  }
});

prismaInstance.$on("error", (e) => {
  console.error("Prisma Error:", e);
});

prismaInstance.$on("warn", (e) => {
  console.warn("Prisma Warning:", e);
});

prismaInstance.$on("info", (e) => {
  console.info("Prisma Info:", e);
});

export const prisma = globalForPrisma.prisma ?? prismaInstance;

// Keep Prisma client instance in development to avoid too many connections
if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

// Add connection pool monitoring
if (process.env.NODE_ENV === "production") {
  setInterval(async () => {
    try {
      await prisma.$queryRaw`SELECT 1`;
    } catch (error) {
      console.error("Database health check failed:", error);
    }
  }, 30000); // Check every 30 seconds
}

/**
 * Database connection helper with error handling
 * @returns Promise that resolves when connected or rejects with error
 */
export async function connectToDatabase() {
  try {
    if (!DATABASE_CONFIG.URL) {
      throw new Error("DATABASE_URL environment variable is not set");
    }

    await prisma.$connect();
    console.log("Connected to database successfully");
    return true;
  } catch (error) {
    console.error("Failed to connect to database:", error);

    // Provide more helpful error messages based on error type
    if (error instanceof Error) {
      if (error.message.includes("connect ECONNREFUSED")) {
        console.error(
          `Database server is not running or not accessible at ${DATABASE_CONFIG.HOST}:${DATABASE_CONFIG.PORT}`,
        );
      } else if (error.message.includes("authentication failed")) {
        console.error(
          "Database authentication failed - check username and password",
        );
      } else if (error.message.includes("does not exist")) {
        console.error(
          `Database '${DATABASE_CONFIG.NAME}' does not exist - you may need to create it first`,
        );
      }
    }

    return false;
  }
}

/**
 * Graceful shutdown of database connection
 */
export async function disconnectFromDatabase() {
  try {
    await prisma.$disconnect();
    console.log("Disconnected from database");
  } catch (error) {
    console.error("Error disconnecting from database:", error);
  }
}

/**
 * Utility function to check database connection
 * @returns Promise that resolves to boolean indicating connection status
 */
export async function isDatabaseConnected(): Promise<boolean> {
  try {
    // Execute a simple query to check connection
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error("Database connection check failed:", error);
    return false;
  }
}
