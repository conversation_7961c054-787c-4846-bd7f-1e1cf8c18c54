/**
 * React Hook for AG-UI Integration
 * Provides easy access to AG-UI client functionality in React components
 */

import { useEffect, useState, useCallback, useRef } from 'react';
import { getAgUIClient, TutorAIState } from '@/lib/ag-ui-client';
import { AgentEvent } from '@ag-ui/core';

export interface UseAgUIOptions {
  autoConnect?: boolean;
  reconnectOnError?: boolean;
}

export interface UseAgUIReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  
  // Agent state
  agentState: TutorAIState;
  
  // Actions
  connect: () => Promise<void>;
  disconnect: () => void;
  sendMessage: (message: string, context?: any) => Promise<void>;
  requestTutorial: (pageUrl: string, userQuery?: string) => Promise<void>;
  explainElement: (selector: string, question?: string) => Promise<void>;
  
  // Event handling
  addEventListener: (eventType: string, listener: (event: AgentEvent) => void) => void;
  removeEventListener: (eventType: string, listener: (event: AgentEvent) => void) => void;
  
  // Real-time messages
  messages: Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
  }>;
  
  // Tutorial state
  currentStep: number;
  totalSteps: number;
  tutorialProgress: Record<string, any>;
}

export function useAgUI(options: UseAgUIOptions = {}): UseAgUIReturn {
  const { autoConnect = true, reconnectOnError = true } = options;
  
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [agentState, setAgentState] = useState<TutorAIState>({
    currentStep: 0,
    totalSteps: 0,
    tutorialProgress: {},
    userPreferences: {}
  });
  const [messages, setMessages] = useState<Array<{
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    isStreaming?: boolean;
  }>>([]);

  const clientRef = useRef(getAgUIClient());
  const eventListenersRef = useRef<Map<string, ((event: AgentEvent) => void)[]>>(new Map());
  const currentMessageRef = useRef<string>('');

  // Handle AG-UI events
  const handleAgentEvent = useCallback((event: AgentEvent) => {
    switch (event.type) {
      case 'RunStarted':
        setConnectionError(null);
        break;

      case 'RunError':
        const errorEvent = event as any;
        setConnectionError(errorEvent.message);
        break;

      case 'TextMessageStart':
        const startEvent = event as any;
        currentMessageRef.current = '';
        setMessages(prev => [...prev, {
          id: startEvent.messageId,
          role: startEvent.role,
          content: '',
          timestamp: new Date(),
          isStreaming: true
        }]);
        break;

      case 'TextMessageContent':
        const contentEvent = event as any;
        currentMessageRef.current += contentEvent.delta;
        setMessages(prev => prev.map(msg => 
          msg.id === contentEvent.messageId 
            ? { ...msg, content: currentMessageRef.current }
            : msg
        ));
        break;

      case 'TextMessageEnd':
        const endEvent = event as any;
        setMessages(prev => prev.map(msg => 
          msg.id === endEvent.messageId 
            ? { ...msg, isStreaming: false }
            : msg
        ));
        currentMessageRef.current = '';
        break;

      case 'StateSnapshot':
        const snapshotEvent = event as any;
        setAgentState(prev => ({ ...prev, ...snapshotEvent.snapshot }));
        break;

      case 'StateDelta':
        const deltaEvent = event as any;
        // Apply JSON Patch operations to state
        setAgentState(prev => {
          const newState = { ...prev };
          deltaEvent.delta.forEach((operation: any) => {
            applyJsonPatch(newState, operation);
          });
          return newState;
        });
        break;

      case 'StepStarted':
        const stepStartedEvent = event as any;
        setAgentState(prev => ({
          ...prev,
          currentStep: parseInt(stepStartedEvent.stepName.replace('step-', '')) || prev.currentStep
        }));
        break;

      case 'ToolCallStart':
        // Handle tool calls (e.g., element highlighting)
        const toolStartEvent = event as any;
        if (toolStartEvent.toolCallName === 'highlight_element') {
          // Tool call started - could show loading indicator
        }
        break;

      case 'ToolCallResult':
        // Handle tool results
        const toolResultEvent = event as any;
        console.log('Tool result:', toolResultEvent.content);
        break;

      case 'Custom':
        const customEvent = event as any;
        if (customEvent.name === 'connection_established') {
          setIsConnected(true);
          setIsConnecting(false);
          setConnectionError(null);
        }
        break;
    }

    // Notify custom event listeners
    const listeners = eventListenersRef.current.get(event.type) || [];
    listeners.forEach(listener => listener(event));

    // Notify wildcard listeners
    const wildcardListeners = eventListenersRef.current.get('*') || [];
    wildcardListeners.forEach(listener => listener(event));
  }, []);

  // Simple JSON Patch implementation
  const applyJsonPatch = (target: any, operation: any) => {
    const { op, path, value } = operation;
    const pathParts = path.split('/').filter(Boolean);
    
    let current = target;
    for (let i = 0; i < pathParts.length - 1; i++) {
      current = current[pathParts[i]];
    }
    
    const lastKey = pathParts[pathParts.length - 1];
    
    switch (op) {
      case 'replace':
      case 'add':
        current[lastKey] = value;
        break;
      case 'remove':
        delete current[lastKey];
        break;
    }
  };

  // Connect to AG-UI server
  const connect = useCallback(async () => {
    if (isConnected || isConnecting) return;

    setIsConnecting(true);
    setConnectionError(null);

    try {
      await clientRef.current.connect();
      // Connection success will be handled by the 'connection_established' event
    } catch (error) {
      setIsConnecting(false);
      setConnectionError(error instanceof Error ? error.message : 'Connection failed');
      
      if (reconnectOnError) {
        // Retry connection after delay
        setTimeout(connect, 5000);
      }
    }
  }, [isConnected, isConnecting, reconnectOnError]);

  // Disconnect from AG-UI server
  const disconnect = useCallback(() => {
    clientRef.current.disconnect();
    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
  }, []);

  // Send message to agent
  const sendMessage = useCallback(async (message: string, context?: any) => {
    if (!isConnected) {
      throw new Error('Not connected to AG-UI server');
    }

    // Add user message to messages
    setMessages(prev => [...prev, {
      id: `user_${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date()
    }]);

    await clientRef.current.sendMessage(message, context);
  }, [isConnected]);

  // Request tutorial for current page
  const requestTutorial = useCallback(async (pageUrl: string, userQuery?: string) => {
    if (!isConnected) {
      throw new Error('Not connected to AG-UI server');
    }

    await clientRef.current.requestTutorial(pageUrl, userQuery);
  }, [isConnected]);

  // Explain specific element
  const explainElement = useCallback(async (selector: string, question?: string) => {
    if (!isConnected) {
      throw new Error('Not connected to AG-UI server');
    }

    await clientRef.current.explainElement(selector, question);
  }, [isConnected]);

  // Add event listener
  const addEventListener = useCallback((eventType: string, listener: (event: AgentEvent) => void) => {
    if (!eventListenersRef.current.has(eventType)) {
      eventListenersRef.current.set(eventType, []);
    }
    eventListenersRef.current.get(eventType)!.push(listener);
  }, []);

  // Remove event listener
  const removeEventListener = useCallback((eventType: string, listener: (event: AgentEvent) => void) => {
    const listeners = eventListenersRef.current.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }, []);

  // Set up event listener on mount
  useEffect(() => {
    clientRef.current.on('*', handleAgentEvent);

    if (autoConnect) {
      connect();
    }

    return () => {
      clientRef.current.off('*', handleAgentEvent);
      disconnect();
    };
  }, [autoConnect, connect, disconnect, handleAgentEvent]);

  // Update agent state from client
  useEffect(() => {
    const updateState = () => {
      const currentState = clientRef.current.getState();
      setAgentState(currentState);
    };

    const interval = setInterval(updateState, 1000); // Update every second
    return () => clearInterval(interval);
  }, []);

  return {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    
    // Agent state
    agentState,
    
    // Actions
    connect,
    disconnect,
    sendMessage,
    requestTutorial,
    explainElement,
    
    // Event handling
    addEventListener,
    removeEventListener,
    
    // Real-time messages
    messages,
    
    // Tutorial state
    currentStep: agentState.currentStep,
    totalSteps: agentState.totalSteps,
    tutorialProgress: agentState.tutorialProgress
  };
}
