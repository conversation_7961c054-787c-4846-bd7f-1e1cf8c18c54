import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { rateLimit } from "@/lib/rate-limit";
import { RATE_LIMIT_CONFIG } from "@/lib/config";

// Rate limiters for different endpoints
const authLimiter = rateLimit({
  interval: RATE_LIMIT_CONFIG.auth.windowMs,
  uniqueTokenPerInterval: 500,
});

const apiLimiter = rateLimit({
  interval: RATE_LIMIT_CONFIG.api.windowMs,
  uniqueTokenPerInterval: 1000,
});

const passwordResetLimiter = rateLimit({
  interval: RATE_LIMIT_CONFIG.passwordReset.windowMs,
  uniqueTokenPerInterval: 500,
});

// CSRF token validation
function validateCSRFToken(request: NextRequest): boolean {
  if (request.method === "GET" || request.method === "HEAD") {
    return true;
  }

  const csrfToken =
    request.headers.get("x-csrf-token") ||
    request.headers.get("x-requested-with");

  // In production, implement proper CSRF token validation
  // For now, we'll check for the presence of the header
  return (
    !!csrfToken ||
    request.headers.get("content-type")?.includes("application/json")
  );
}

// Security headers
function addSecurityHeaders(response: NextResponse): NextResponse {
  // Prevent clickjacking
  response.headers.set("X-Frame-Options", "DENY");

  // Prevent MIME type sniffing
  response.headers.set("X-Content-Type-Options", "nosniff");

  // Enable XSS protection
  response.headers.set("X-XSS-Protection", "1; mode=block");

  // Strict transport security (HTTPS only)
  if (process.env.NODE_ENV === "production") {
    response.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains",
    );
  }

  // Content Security Policy
  response.headers.set(
    "Content-Security-Policy",
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;",
  );

  // Referrer policy
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ip = request.ip ?? request.headers.get("x-forwarded-for") ?? "unknown";

  // Apply rate limiting
  let limiter;
  let limit;

  if (pathname.startsWith("/api/auth/")) {
    limiter = authLimiter;
    limit = RATE_LIMIT_CONFIG.auth.max;
  } else if (
    pathname.includes("/forgot-password") ||
    pathname.includes("/reset-password")
  ) {
    limiter = passwordResetLimiter;
    limit = RATE_LIMIT_CONFIG.passwordReset.max;
  } else if (pathname.startsWith("/api/")) {
    limiter = apiLimiter;
    limit = RATE_LIMIT_CONFIG.api.max;
  }

  if (limiter && limit) {
    const { success, remaining, reset } = await limiter.check(limit, ip);

    if (!success) {
      return new NextResponse(JSON.stringify({ error: "Too many requests" }), {
        status: 429,
        headers: {
          "Content-Type": "application/json",
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  // CSRF protection for API routes
  if (pathname.startsWith("/api/") && !pathname.startsWith("/api/auth/")) {
    if (!validateCSRFToken(request)) {
      return new NextResponse(
        JSON.stringify({ error: "CSRF token missing or invalid" }),
        {
          status: 403,
          headers: { "Content-Type": "application/json" },
        },
      );
    }
  }

  // Admin route protection
  if (pathname.startsWith("/admin")) {
    const token = await getToken({ req: request });

    if (!token) {
      return NextResponse.redirect(new URL("/auth/signin", request.url));
    }

    if (!["ADMIN", "SUPER_ADMIN"].includes(token.role as string)) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
  }

  // Create response and add security headers
  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
