export interface Tutorial {
  id: string;
  title: string;
  description?: string;
  steps: TutorialStep[];
  language: string;
  isActive: boolean;
  metadata: TutorialMetadata;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  selector?: string;
  action: "click" | "hover" | "type" | "scroll" | "wait" | "explain";
  content: string;
  voiceContent?: string;
  position: {
    x: number;
    y: number;
  };
  duration?: number;
  conditions?: {
    url?: string;
    element?: string;
    text?: string;
  };
}

export interface TutorialMetadata {
  category: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: number;
  tags: string[];
  targetUrl?: string;
  version: string;
  analytics: {
    views: number;
    completions: number;
    averageRating: number;
    feedback: TutorialFeedback[];
  };
}

export interface TutorialFeedback {
  userId: string;
  rating: number;
  comment?: string;
  timestamp: Date;
}

export interface CreateTutorialData {
  title: string;
  description?: string;
  steps: Omit<TutorialStep, "id">[];
  language?: string;
  metadata: Omit<TutorialMetadata, "analytics">;
}

export interface UpdateTutorialData {
  title?: string;
  description?: string;
  steps?: TutorialStep[];
  language?: string;
  isActive?: boolean;
  metadata?: Partial<TutorialMetadata>;
}

export interface TutorialProgress {
  tutorialId: string;
  userId: string;
  currentStep: number;
  completed: boolean;
  startedAt: Date;
  completedAt?: Date;
  timeSpent: number;
}
