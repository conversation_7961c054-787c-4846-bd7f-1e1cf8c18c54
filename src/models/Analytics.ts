export interface Analytics {
  id: string;
  userId: string;
  tutorialId?: string;
  action: AnalyticsAction;
  metadata: AnalyticsMetadata;
  timestamp: Date;
}

export type AnalyticsAction =
  | "tutorial_started"
  | "tutorial_completed"
  | "tutorial_abandoned"
  | "step_completed"
  | "step_skipped"
  | "voice_used"
  | "ai_explanation_requested"
  | "element_highlighted"
  | "error_occurred"
  | "feedback_submitted"
  | "settings_changed"
  | "login"
  | "logout";

export interface AnalyticsMetadata {
  url?: string;
  element?: string;
  stepId?: string;
  duration?: number;
  errorMessage?: string;
  userAgent?: string;
  sessionId?: string;
  aiProvider?: string;
  voiceProvider?: string;
  language?: string;
  customData?: Record<string, any>;
}

export interface CreateAnalyticsData {
  userId: string;
  tutorialId?: string;
  action: AnalyticsAction;
  metadata?: AnalyticsMetadata;
}

export interface AnalyticsDashboard {
  totalUsers: number;
  activeUsers: number;
  totalTutorials: number;
  completionRate: number;
  averageSessionTime: number;
  topTutorials: {
    id: string;
    title: string;
    views: number;
    completions: number;
  }[];
  userGrowth: {
    date: string;
    users: number;
  }[];
  usageByProvider: {
    provider: string;
    usage: number;
    cost: number;
  }[];
}

export interface UsageStatistics {
  period: "day" | "week" | "month" | "year";
  tutorialViews: number;
  tutorialCompletions: number;
  aiRequests: number;
  voiceRequests: number;
  averageRating: number;
  userRetention: number;
}
