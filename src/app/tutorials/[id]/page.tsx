"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ThemeSwitcher } from "@/components/theme-switcher";
import { useToast } from "@/components/ui/use-toast";
import {
  Sparkles,
  ArrowLeft,
  Clock,
  Users,
  Star,
  PlayCircle,
  CheckCircle,
  BookOpen,
} from "lucide-react";
import { Tutorial } from "@/models/Tutorial";
import TutorialPlayer from "@/components/tutorial/TutorialPlayer";
import { useSession } from "next-auth/react";

export default function TutorialDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const { toast } = useToast();
  const [tutorial, setTutorial] = useState<Tutorial | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [isPlayerOpen, setIsPlayerOpen] = useState(false);
  const [userProgress, setUserProgress] = useState<{
    progress: number;
    isCompleted: boolean;
  }>({
    progress: 0,
    isCompleted: false,
  });

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (status === "authenticated") {
      fetchTutorial();
    }
  }, [status, id, router]);

  const fetchTutorial = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tutorials/${id}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch tutorial");
      }
      
      const data = await response.json();
      setTutorial(data.tutorial);
      
      // Fetch user progress if available
      if (data.userProgress) {
        setUserProgress({
          progress: data.userProgress.progress || 0,
          isCompleted: data.userProgress.completed || false,
        });
      }
    } catch (error) {
      console.error("Error fetching tutorial:", error);
      setError("Failed to load tutorial. Please try again.");
      toast({
        title: "Error",
        description: "Failed to load tutorial. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartTutorial = () => {
    setIsPlayerOpen(true);
  };

  const handleCompleteTutorial = async () => {
    try {
      // Update user progress
      await fetch(`/api/tutorials/${id}/progress`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          completed: true,
          progress: 100,
        }),
      });

      setUserProgress({
        progress: 100,
        isCompleted: true,
      });

      toast({
        title: "Tutorial Completed",
        description: "Congratulations! You've completed this tutorial.",
        variant: "default",
      });
    } catch (error) {
      console.error("Error updating tutorial progress:", error);
    } finally {
      setIsPlayerOpen(false);
    }
  };

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tutorial...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (error || !tutorial) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Tutorial not found</h3>
          <p className="text-muted-foreground mb-4">
            The tutorial you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link href="/tutorials">Back to Tutorials</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/tutorials">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Tutorials
              </Button>
            </Link>
            <div className="flex items-center gap-2">
              <Sparkles className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">TutorAI</span>
            </div>
          </div>
          <ThemeSwitcher />
        </div>
      </header>

      {/* Main Content */}
      <div className="container py-8">
        <div className="flex flex-col gap-8">
          {/* Tutorial Header */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-wrap items-center gap-3">
              <Badge
                variant={
                  tutorial.metadata.difficulty === "beginner"
                    ? "secondary"
                    : tutorial.metadata.difficulty === "intermediate"
                    ? "default"
                    : "destructive"
                }
              >
                {tutorial.metadata.difficulty}
              </Badge>
              <Badge variant="outline">{tutorial.metadata.category}</Badge>
            </div>
            <h1 className="text-3xl font-bold tracking-tight">
              {tutorial.title}
            </h1>
            <p className="text-muted-foreground">{tutorial.description}</p>

            <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{tutorial.metadata.estimatedTime} min</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{tutorial.metadata.analytics.views}</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4" />
                <span>
                  {tutorial.metadata.analytics.averageRating.toFixed(1)}
                </span>
              </div>
            </div>

            {userProgress.progress > 0 && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{userProgress.progress}%</span>
                </div>
                <Progress value={userProgress.progress} className="h-2" />
              </div>
            )}

            <div className="flex gap-4 mt-2">
              <Button
                className="gap-2"
                onClick={handleStartTutorial}
                disabled={userProgress.isCompleted}
              >
                {userProgress.isCompleted ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    <span>Completed</span>
                  </>
                ) : (
                  <>
                    <PlayCircle className="h-4 w-4" />
                    <span>
                      {userProgress.progress > 0
                        ? "Continue Tutorial"
                        : "Start Tutorial"}
                    </span>
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Tutorial Details */}
          <Card>
            <CardHeader>
              <CardTitle>About this Tutorial</CardTitle>
              <CardDescription>
                Learn what you'll accomplish in this tutorial
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-muted-foreground">{tutorial.description}</p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Steps Overview</h3>
                <ul className="space-y-2">
                  {tutorial.steps.map((step, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary/10 text-primary text-xs">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{step.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {step.description}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Tutorial Player */}
      {isPlayerOpen && tutorial && (
        <TutorialPlayer
          tutorial={tutorial}
          onComplete={handleCompleteTutorial}
          onClose={() => setIsPlayerOpen(false)}
        />
      )}
    </div>
  );
} 