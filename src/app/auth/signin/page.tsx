"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { FormError } from "@/components/ui/form-error";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  Sparkles,
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowLeft,
  Brain,
  Zap,
  Users,
  Shield,
} from "lucide-react";
import { signIn, getSession, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { signInSchema, type SignInFormData } from "@/lib/validation";
import { TOAST_CONFIG, AUTH_CONFIG } from "@/lib/constants";

export default function SignInPage() {
  const [formData, setFormData] = useState<SignInFormData>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<SignInFormData>>({});
  const [generalError, setGeneralError] = useState("");
  const router = useRouter();
  const { data: session } = useSession();
  const { toast } = useToast();

  // Redirect if already authenticated
  useEffect(() => {
    if (session) {
      router.push("/dashboard");
    }
  }, [session, router]);

  const validateForm = (): boolean => {
    const result = signInSchema.safeParse(formData);

    if (!result.success) {
      const fieldErrors: Partial<SignInFormData> = {};
      Object.entries(result.error.flatten().fieldErrors).forEach(
        ([field, error]) => {
          fieldErrors[field as keyof SignInFormData] = error[0] || "";
        },
      );
      setErrors(fieldErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  const handleInputChange = (field: keyof SignInFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    // Clear general error
    if (generalError) {
      setGeneralError("");
    }
  };

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below and try again.",
        variant: TOAST_CONFIG.variants.error,
      });
      return;
    }

    setIsLoading(true);
    setGeneralError("");

    try {
      const result = await signIn("credentials", {
        email: formData.email,
        password: formData.password,
        redirect: false,
      });

      if (result?.error) {
        const errorMessage =
          "Invalid credentials. Please check your email and password.";
        setGeneralError(errorMessage);
        toast({
          title: "Sign in failed",
          description: errorMessage,
          variant: TOAST_CONFIG.variants.error,
        });
      } else {
        toast({
          title: "Welcome back!",
          description: "You have been signed in successfully.",
          variant: TOAST_CONFIG.variants.success,
        });
        const session = await getSession();
        if (session) {
          router.push(AUTH_CONFIG.redirects.afterSignIn);
        }
      }
    } catch (error) {
      const errorMessage = "An unexpected error occurred. Please try again.";
      setGeneralError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: TOAST_CONFIG.variants.error,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn("google", {
        callbackUrl: AUTH_CONFIG.redirects.afterSignIn,
      });
    } catch (error) {
      toast({
        title: "Google Sign In Failed",
        description: "Unable to sign in with Google. Please try again.",
        variant: TOAST_CONFIG.variants.error,
      });
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="grid lg:grid-cols-2 min-h-screen">
        {/* Left Column - Branding */}
        <div className="hidden lg:flex flex-col justify-center p-12 bg-gradient-to-br from-primary/10 via-primary/5 to-background relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/20 via-transparent to-transparent" />
          <div className="absolute top-20 right-20 w-32 h-32 bg-primary/10 rounded-full blur-xl" />
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-secondary/10 rounded-full blur-xl" />

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative z-10 max-w-md"
          >
            <Link href="/" className="flex items-center gap-3 mb-8">
              <Sparkles className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">TutorAI</span>
            </Link>

            <h1 className="text-4xl font-bold tracking-tight mb-6">
              Welcome back to the future of learning
            </h1>

            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Continue your AI-powered learning journey with personalized
              tutorials, voice guidance, and intelligent explanations for any
              webpage.
            </p>

            <div className="space-y-4">
              {[
                { icon: Brain, text: "AI-powered explanations" },
                { icon: Zap, text: "Real-time guidance" },
                { icon: Users, text: "Trusted by 12,000+ learners" },
              ].map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <span className="text-muted-foreground">
                      {feature.text}
                    </span>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>

        {/* Right Column - Form */}
        <div className="flex flex-col">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 lg:hidden">
            <div className="container flex h-16 items-center justify-between">
              <Link href="/" className="flex items-center gap-2">
                <Sparkles className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">TutorAI</span>
              </Link>
              <ThemeSwitcher />
            </div>
          </header>

          <div className="hidden lg:flex items-center justify-between p-6 border-b">
            <Link
              href="/"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to home</span>
            </Link>
            <ThemeSwitcher />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center p-6 lg:p-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="w-full max-w-md space-y-8"
            >
              <div className="text-center space-y-2">
                <h2 className="text-3xl font-bold tracking-tight">
                  Welcome back
                </h2>
                <p className="text-muted-foreground">
                  Sign in to your TutorAI account to continue your learning
                  journey
                </p>
              </div>

              <div className="space-y-6">
                {/* Google Sign In */}
                <Button
                  variant="outline"
                  className="w-full h-12"
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                >
                  <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-3 text-muted-foreground">
                      Or continue with email
                    </span>
                  </div>
                </div>

                {/* Email Sign In Form */}
                <form onSubmit={handleEmailSignIn} className="space-y-5">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        value={formData.email}
                        onChange={(e) =>
                          handleInputChange("email", e.target.value)
                        }
                        className="pl-10 h-12"
                        error={!!errors.email}
                        required
                      />
                    </div>
                    <FormError message={errors.email} />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        value={formData.password}
                        onChange={(e) =>
                          handleInputChange("password", e.target.value)
                        }
                        className="pl-10 pr-12 h-12"
                        error={!!errors.password}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-1/2 transform -translate-y-1/2 h-full px-3 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <FormError message={errors.password} />
                  </div>

                  {generalError && (
                    <Alert variant="destructive">
                      <AlertDescription>{generalError}</AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full h-12"
                    disabled={isLoading}
                  >
                    {isLoading ? "Signing in..." : "Sign In"}
                  </Button>
                </form>

                <div className="text-center text-sm">
                  <Link
                    href="/auth/forgot-password"
                    className="text-primary hover:underline font-medium"
                  >
                    Forgot your password?
                  </Link>
                </div>

                <div className="text-center text-sm text-muted-foreground">
                  Don&apos;t have an account?{" "}
                  <Link
                    href="/auth/signup"
                    className="text-primary hover:underline font-medium"
                  >
                    Sign up
                  </Link>
                </div>
              </div>

              {/* Footer Links */}
              <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground pt-8">
                <Link
                  href="/"
                  className="hover:text-foreground transition-colors"
                >
                  Home
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-foreground transition-colors"
                >
                  Terms
                </Link>
                <Link
                  href="/privacy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
