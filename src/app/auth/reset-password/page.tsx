"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  <PERSON>rk<PERSON>,
  Lock,
  ArrowLeft,
  CheckCircle,
  Eye,
  EyeOff,
  Shield,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { z } from "zod";
import { TOAST_CONFIG } from "@/lib/constants";

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const [formData, setFormData] = useState<ResetPasswordFormData>({
    password: "",
    confirmPassword: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState<Partial<ResetPasswordFormData>>({});
  const [generalError, setGeneralError] = useState("");
  const [token, setToken] = useState<string | null>(null);
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);

  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  useEffect(() => {
    const tokenParam = searchParams.get("token");
    if (!tokenParam) {
      setGeneralError("Missing reset token. Please request a new password reset link.");
      setTokenValid(false);
      return;
    }
    
    setToken(tokenParam);
    setTokenValid(true);
  }, [searchParams]);

  const validateForm = (): boolean => {
    const result = resetPasswordSchema.safeParse(formData);

    if (!result.success) {
      const fieldErrors: Partial<ResetPasswordFormData> = {};
      Object.entries(result.error.flatten().fieldErrors).forEach(
        ([field, error]) => {
          fieldErrors[field as keyof ResetPasswordFormData] = error[0] || "";
        },
      );
      setErrors(fieldErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  const handleInputChange = (
    field: keyof ResetPasswordFormData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    // Clear general error
    if (generalError) {
      setGeneralError("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!token) {
      setGeneralError("Missing reset token. Please request a new password reset link.");
      return;
    }

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors below and try again.",
        variant: TOAST_CONFIG.variants.error,
      });
      return;
    }

    setIsLoading(true);
    setGeneralError("");

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password: formData.password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorMessage = data.error || "Failed to reset password. Please try again.";
        setGeneralError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: TOAST_CONFIG.variants.error,
        });
      } else {
        setIsSuccess(true);
        toast({
          title: "Success",
          description: "Your password has been reset successfully.",
          variant: TOAST_CONFIG.variants.success,
        });
      }
    } catch (error) {
      const errorMessage = "An unexpected error occurred. Please try again.";
      setGeneralError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: TOAST_CONFIG.variants.error,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="grid lg:grid-cols-2 min-h-screen">
        {/* Left Column - Branding */}
        <div className="hidden lg:flex flex-col justify-center p-12 bg-gradient-to-br from-primary/10 via-primary/5 to-background relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/20 via-transparent to-transparent" />
          <div className="absolute top-20 right-20 w-32 h-32 bg-primary/10 rounded-full blur-xl" />
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-secondary/10 rounded-full blur-xl" />

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative z-10 max-w-md"
          >
            <Link href="/" className="flex items-center gap-3 mb-8">
              <Sparkles className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">TutorAI</span>
            </Link>

            <h1 className="text-4xl font-bold tracking-tight mb-6">
              Reset your password
            </h1>

            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Create a new secure password for your account and get back to your AI-powered learning journey.
            </p>

            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-primary" />
              <span className="text-muted-foreground">
                Secure password reset process
              </span>
            </div>
          </motion.div>
        </div>

        {/* Right Column - Form */}
        <div className="flex flex-col">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 lg:hidden">
            <div className="container flex h-16 items-center justify-between">
              <Link href="/" className="flex items-center gap-2">
                <Sparkles className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">TutorAI</span>
              </Link>
              <ThemeSwitcher />
            </div>
          </header>

          <div className="hidden lg:flex items-center justify-between p-6 border-b">
            <Link
              href="/auth/signin"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to sign in</span>
            </Link>
            <ThemeSwitcher />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center p-6 lg:p-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="w-full max-w-md space-y-8"
            >
              {tokenValid === false && (
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto">
                    <Shield className="h-8 w-8 text-red-600 dark:text-red-400" />
                  </div>

                  <div className="space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                      Invalid Token
                    </h2>
                    <p className="text-muted-foreground">
                      The password reset link is invalid or has expired.
                    </p>
                  </div>

                  <Button
                    variant="default"
                    asChild
                    className="w-full"
                  >
                    <Link href="/auth/forgot-password">
                      Request New Reset Link
                    </Link>
                  </Button>
                </div>
              )}

              {tokenValid === true && !isSuccess ? (
                <>
                  <div className="text-center space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                      Create New Password
                    </h2>
                    <p className="text-muted-foreground">
                      Enter a new password for your account
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="password" className="text-sm font-medium">
                        New Password
                      </Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter your new password"
                          value={formData.password}
                          onChange={(e) => handleInputChange("password", e.target.value)}
                          className="pl-10 pr-10 h-12"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {errors.password && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.password}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword" className="text-sm font-medium">
                        Confirm Password
                      </Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm your new password"
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                          className="pl-10 pr-10 h-12"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {errors.confirmPassword && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.confirmPassword}
                        </p>
                      )}
                    </div>

                    {generalError && (
                      <Alert variant="destructive">
                        <AlertDescription>{generalError}</AlertDescription>
                      </Alert>
                    )}

                    <Button
                      type="submit"
                      className="w-full h-12"
                      disabled={isLoading}
                    >
                      {isLoading ? "Resetting Password..." : "Reset Password"}
                    </Button>
                  </form>
                </>
              ) : tokenValid === true && isSuccess ? (
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>

                  <div className="space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                      Password Reset Successful
                    </h2>
                    <p className="text-muted-foreground">
                      Your password has been reset successfully. You can now sign in with your new password.
                    </p>
                  </div>

                  <Button
                    variant="default"
                    asChild
                    className="w-full"
                  >
                    <Link href="/auth/signin">
                      Sign In
                    </Link>
                  </Button>
                </div>
              ) : null}

              <div className="text-center text-sm text-muted-foreground">
                Remember your password?{" "}
                <Link
                  href="/auth/signin"
                  className="text-primary hover:underline font-medium"
                >
                  Sign in
                </Link>
              </div>

              {/* Footer Links */}
              <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground pt-8">
                <Link
                  href="/"
                  className="hover:text-foreground transition-colors"
                >
                  Home
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-foreground transition-colors"
                >
                  Terms
                </Link>
                <Link
                  href="/privacy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
} 