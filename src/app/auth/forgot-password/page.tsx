"use client";

import React, { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  Sparkles,
  Mail,
  ArrowLeft,
  CheckCircle,
  Brain,
  Shield,
  Zap,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { TOAST_CONFIG } from "@/lib/constants";
import { z } from "zod";

const forgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validate email
    const validationResult = forgotPasswordSchema.safeParse({ email });
    if (!validationResult.success) {
      setError(validationResult.error.flatten().fieldErrors.email?.[0] || "Invalid email");
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send reset email");
      }

      setIsSuccess(true);
      toast({
        title: "Reset link sent!",
        description: "Check your email for password reset instructions.",
        variant: TOAST_CONFIG.variants.success,
      });

      // If in development, show the reset link
      if (process.env.NODE_ENV === "development" && data.devInfo?.resetLink) {
        console.info("Development reset link:", data.devInfo.resetLink);
      }
    } catch (error) {
      setError("Failed to send reset email. Please try again.");
      toast({
        title: "Error",
        description: "Failed to send reset email. Please try again.",
        variant: TOAST_CONFIG.variants.error,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="grid lg:grid-cols-2 min-h-screen">
        {/* Left Column - Branding */}
        <div className="hidden lg:flex flex-col justify-center p-12 bg-gradient-to-br from-primary/10 via-primary/5 to-background relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/20 via-transparent to-transparent" />
          <div className="absolute top-20 right-20 w-32 h-32 bg-primary/10 rounded-full blur-xl" />
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-secondary/10 rounded-full blur-xl" />

          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="relative z-10 max-w-md"
          >
            <Link href="/" className="flex items-center gap-3 mb-8">
              <Sparkles className="h-8 w-8 text-primary" />
              <span className="text-2xl font-bold">TutorAI</span>
            </Link>

            <h1 className="text-4xl font-bold tracking-tight mb-6">
              Secure account recovery
            </h1>

            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Don't worry, it happens to the best of us. We'll help you get back
              to your AI-powered learning journey in no time.
            </p>

            <div className="space-y-4">
              {[
                { icon: Shield, text: "Secure password reset process" },
                { icon: Brain, text: "Keep your learning progress" },
                { icon: Zap, text: "Quick and easy recovery" },
              ].map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <Icon className="h-5 w-5 text-primary" />
                    <span className="text-muted-foreground">
                      {feature.text}
                    </span>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </div>

        {/* Right Column - Form */}
        <div className="flex flex-col">
          {/* Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 lg:hidden">
            <div className="container flex h-16 items-center justify-between">
              <Link href="/" className="flex items-center gap-2">
                <Sparkles className="h-6 w-6 text-primary" />
                <span className="text-xl font-bold">TutorAI</span>
              </Link>
              <ThemeSwitcher />
            </div>
          </header>

          <div className="hidden lg:flex items-center justify-between p-6 border-b">
            <Link
              href="/auth/signin"
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to sign in</span>
            </Link>
            <ThemeSwitcher />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center p-6 lg:p-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="w-full max-w-md space-y-8"
            >
              {!isSuccess ? (
                <>
                  <div className="text-center space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                      Forgot your password?
                    </h2>
                    <p className="text-muted-foreground">
                      Enter your email address and we'll send you a link to
                      reset your password
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm font-medium">
                        Email
                      </Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="pl-10 h-12"
                          required
                        />
                      </div>
                    </div>

                    {error && (
                      <Alert variant="destructive">
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    )}

                    <Button
                      type="submit"
                      className="w-full h-12"
                      disabled={isLoading}
                    >
                      {isLoading ? "Sending reset link..." : "Send reset link"}
                    </Button>
                  </form>
                </>
              ) : (
                <div className="text-center space-y-6">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
                    <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>

                  <div className="space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                      Check your email
                    </h2>
                    <p className="text-muted-foreground">
                      We've sent a password reset link to{" "}
                      <strong>{email}</strong>
                    </p>
                  </div>

                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      Didn't receive the email? Check your spam folder or try
                      again.
                    </p>

                    <Button
                      variant="outline"
                      onClick={() => {
                        setIsSuccess(false);
                        setEmail("");
                      }}
                      className="w-full"
                    >
                      Try again
                    </Button>
                  </div>
                </div>
              )}

              <div className="text-center text-sm text-muted-foreground">
                Remember your password?{" "}
                <Link
                  href="/auth/signin"
                  className="text-primary hover:underline font-medium"
                >
                  Sign in
                </Link>
              </div>

              {/* Footer Links */}
              <div className="flex items-center justify-center gap-6 text-xs text-muted-foreground pt-8">
                <Link
                  href="/"
                  className="hover:text-foreground transition-colors"
                >
                  Home
                </Link>
                <Link
                  href="/terms"
                  className="hover:text-foreground transition-colors"
                >
                  Terms
                </Link>
                <Link
                  href="/privacy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
