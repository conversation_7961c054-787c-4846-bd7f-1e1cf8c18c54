"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Book<PERSON>pen,
  Zap,
  Activity,
  ChevronRight,
  LogOut,
  User,
  Bell,
  Download,
  Sparkles,
  TrendingUp,
  Clock,
  Target,
} from "lucide-react";

interface DashboardStats {
  totalTutorials: number;
  completedTutorials: number;
  aiRequests: number;
  voiceRequests: number;
  averageRating: number;
  timeSpent: number;
}

interface RecentActivity {
  id: string;
  type: string;
  title: string;
  timestamp: string;
  status: "completed" | "in-progress" | "failed";
}

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<DashboardStats>({
    totalTutorials: 0,
    completedTutorials: 0,
    aiRequests: 0,
    voiceRequests: 0,
    averageRating: 0,
    timeSpent: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (status === "authenticated") {
      fetchDashboardData();
    }
  }, [status, router]);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Fetch user usage statistics
      const usageResponse = await fetch("/api/user/usage");
      if (usageResponse.ok) {
        const usageData = await usageResponse.json();
        setStats({
          totalTutorials: usageData.usage.tutorialViews || 0,
          completedTutorials: usageData.usage.tutorialCompletions || 0,
          aiRequests: usageData.usage.aiRequests || 0,
          voiceRequests: usageData.usage.voiceRequests || 0,
          averageRating: 4.5,
          timeSpent: Math.floor((usageData.usage.totalAIRequests || 0) * 2.5),
        });
      }

      // Fetch recent activity from analytics
      const activityResponse = await fetch("/api/analytics/recent");
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(
          activityData.activities.map((activity: any) => ({
            id: activity.id,
            type: activity.action.includes("tutorial")
              ? "tutorial"
              : activity.action.includes("ai")
                ? "ai"
                : "voice",
            title: getActivityTitle(activity.action, activity.metadata),
            timestamp: formatTimestamp(activity.timestamp),
            status: "completed",
          })),
        );
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      // Fallback to empty data instead of mock data
      setStats({
        totalTutorials: 0,
        completedTutorials: 0,
        aiRequests: 0,
        voiceRequests: 0,
        averageRating: 0,
        timeSpent: 0,
      });
      setRecentActivity([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getActivityTitle = (action: string, metadata: any) => {
    switch (action) {
      case "tutorial_started":
        return `Started tutorial: ${metadata?.title || "Unknown"}`;
      case "tutorial_completed":
        return `Completed tutorial: ${metadata?.title || "Unknown"}`;
      case "ai_explanation_requested":
        return `AI explanation requested for ${metadata?.element || "element"}`;
      case "voice_used":
        return `Voice tutorial used`;
      default:
        return `Activity: ${action}`;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/" });
  };

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  const completionRate =
    stats.totalTutorials > 0
      ? (stats.completedTutorials / stats.totalTutorials) * 100
      : 0;

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">TutorAI</span>
          </Link>

          <div className="flex items-center gap-4">
            <ThemeSwitcher />
            <Button variant="ghost" size="icon">
              <Bell className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={session.user?.image || ""}
                      alt={session.user?.name || ""}
                    />
                    <AvatarFallback>
                      {session.user?.name?.charAt(0) ||
                        session.user?.email?.charAt(0) ||
                        "U"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {session.user?.name || "User"}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {session.user?.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <Link href="/profile">
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                </Link>
                <Link href="/settings">
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                </Link>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container py-8">
        <div className="flex flex-col gap-8">
          {/* Welcome Section */}
          <div className="flex flex-col gap-2">
            <h1 className="text-3xl font-bold tracking-tight">
              Welcome back, {session.user?.name?.split(" ")[0] || "User"}!
            </h1>
            <p className="text-muted-foreground">
              Here&apos;s an overview of your learning progress and AI tutoring
              activity.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Tutorials
                </CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalTutorials}</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last week
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Completion Rate
                </CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(completionRate)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {stats.completedTutorials} of {stats.totalTutorials} completed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  AI Requests
                </CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.aiRequests}</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Time Spent
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.timeSpent}h</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>
          </div>

          {/* Main Dashboard Content */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Progress Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle>Learning Progress</CardTitle>
                    <CardDescription>
                      Your tutorial completion progress
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Overall Progress</span>
                        <span>{Math.round(completionRate)}%</span>
                      </div>
                      <Progress value={completionRate} className="h-2" />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>AI Integration Tutorials</span>
                        <span>75%</span>
                      </div>
                      <Progress value={75} className="h-2" />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Voice Commands</span>
                        <span>60%</span>
                      </div>
                      <Progress value={60} className="h-2" />
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                    <CardDescription>
                      Your latest learning activities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {recentActivity.map((activity) => (
                        <div
                          key={activity.id}
                          className="flex items-center space-x-4"
                        >
                          <div className="flex-shrink-0">
                            {activity.type === "tutorial" && (
                              <BookOpen className="h-4 w-4 text-blue-500" />
                            )}
                            {activity.type === "ai" && (
                              <Zap className="h-4 w-4 text-yellow-500" />
                            )}
                            {activity.type === "voice" && (
                              <Activity className="h-4 w-4 text-green-500" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {activity.title}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {activity.timestamp}
                            </p>
                          </div>
                          <Badge
                            variant={
                              activity.status === "completed"
                                ? "default"
                                : activity.status === "in-progress"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {activity.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Get started with common tasks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button className="h-auto p-4 flex flex-col items-center space-y-2">
                      <Download className="h-6 w-6" />
                      <span>Download Extension</span>
                    </Button>
                    <Link href="/tutorials">
                      <Button
                        variant="outline"
                        className="h-auto p-4 flex flex-col items-center space-y-2"
                      >
                        <BookOpen className="h-6 w-6" />
                        <span>Browse Tutorials</span>
                      </Button>
                    </Link>
                    <Button
                      variant="outline"
                      className="h-auto p-4 flex flex-col items-center space-y-2"
                    >
                      <Settings className="h-6 w-6" />
                      <span>Configure AI</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tutorials" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Available Tutorials</CardTitle>
                  <CardDescription>
                    Interactive tutorials to help you master web navigation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      {
                        title: "Getting Started with AI Tutor",
                        description:
                          "Learn the basics of using AI-powered explanations",
                        progress: 100,
                        status: "completed",
                      },
                      {
                        title: "Advanced Web Navigation",
                        description: "Master complex website interactions",
                        progress: 45,
                        status: "in-progress",
                      },
                      {
                        title: "Voice Commands Tutorial",
                        description:
                          "Learn to control the extension with voice",
                        progress: 0,
                        status: "not-started",
                      },
                      {
                        title: "Form Filling Automation",
                        description: "Automate form filling with AI assistance",
                        progress: 0,
                        status: "not-started",
                      },
                    ].map((tutorial, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex-1">
                          <h4 className="font-medium">{tutorial.title}</h4>
                          <p className="text-sm text-muted-foreground mb-2">
                            {tutorial.description}
                          </p>
                          <div className="flex items-center space-x-2">
                            <Progress
                              value={tutorial.progress}
                              className="h-2 w-32"
                            />
                            <span className="text-xs text-muted-foreground">
                              {tutorial.progress}%
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              tutorial.status === "completed"
                                ? "default"
                                : tutorial.status === "in-progress"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {tutorial.status === "completed"
                              ? "Completed"
                              : tutorial.status === "in-progress"
                                ? "In Progress"
                                : "Start"}
                          </Badge>
                          <Button variant="ghost" size="icon">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Usage Statistics</CardTitle>
                    <CardDescription>
                      Your AI tutoring usage over time
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                        <p>Analytics chart would go here</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>AI Provider Usage</CardTitle>
                    <CardDescription>
                      Distribution of AI provider requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">OpenAI</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={65} className="h-2 w-24" />
                          <span className="text-sm text-muted-foreground">
                            65%
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Anthropic</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={25} className="h-2 w-24" />
                          <span className="text-sm text-muted-foreground">
                            25%
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Google AI</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={10} className="h-2 w-24" />
                          <span className="text-sm text-muted-foreground">
                            10%
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Preferences</CardTitle>
                  <CardDescription>
                    Customize your AI tutoring experience
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">AI Provider</h4>
                      <div className="grid grid-cols-3 gap-2">
                        <Button variant="outline" size="sm">
                          OpenAI
                        </Button>
                        <Button variant="outline" size="sm">
                          Anthropic
                        </Button>
                        <Button variant="outline" size="sm">
                          Google AI
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">
                        Voice Settings
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Voice Speed</span>
                          <div className="w-32">
                            <Progress value={75} className="h-2" />
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Voice Commands</span>
                          <Badge>Enabled</Badge>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">
                        Tutorial Preferences
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Auto-start tutorials</span>
                          <Badge>Enabled</Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Difficulty Level</span>
                          <Badge variant="outline">Beginner</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button>Save Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
