"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import AdminLayout from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { BookOpen, Plus, Search, MoreVertical, Edit, Trash, Eye } from "lucide-react";
import { Tutorial } from "@/models/Tutorial";
import { ADMIN_CONFIG } from "@/lib/constants";

export default function TutorialsAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (
      status === "authenticated" &&
      !ADMIN_CONFIG.permissions.allowedRoles.includes(
        (session?.user as any)?.role
      )
    ) {
      router.push("/dashboard");
    } else if (status === "authenticated") {
      fetchTutorials();
    }
  }, [status, session, router]);

  const fetchTutorials = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/tutorials?limit=100");
      if (response.ok) {
        const data = await response.json();
        setTutorials(data.tutorials || []);
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch tutorials",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching tutorials:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this tutorial?")) {
      return;
    }

    try {
      const response = await fetch(`/api/tutorials/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast({
          title: "Tutorial Deleted",
          description: "The tutorial has been deleted successfully",
          variant: "default",
        });
        fetchTutorials();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete tutorial",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting tutorial:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const filteredTutorials = tutorials.filter(
    (tutorial) =>
      tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutorial.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tutorials...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <AdminLayout
      title="Tutorials"
      description="Manage interactive tutorials for your users."
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="relative w-full max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tutorials..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button onClick={() => router.push("/admin/tutorials/create")} className="gap-2">
            <Plus className="h-4 w-4" /> Create Tutorial
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>All Tutorials</span>
            </CardTitle>
            <CardDescription>
              {filteredTutorials.length} tutorials found
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredTutorials.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No tutorials found</h3>
                <p className="text-muted-foreground">
                  {searchQuery
                    ? "Try adjusting your search query."
                    : "Get started by creating your first tutorial."}
                </p>
                {!searchQuery && (
                  <Button
                    onClick={() => router.push("/admin/tutorials/create")}
                    className="mt-6"
                  >
                    Create Tutorial
                  </Button>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Difficulty</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTutorials.map((tutorial) => (
                      <TableRow key={tutorial.id}>
                        <TableCell className="font-medium">
                          {tutorial.title}
                        </TableCell>
                        <TableCell>
                          {tutorial.metadata.category}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              tutorial.metadata.difficulty === "beginner"
                                ? "secondary"
                                : tutorial.metadata.difficulty === "intermediate"
                                ? "default"
                                : "destructive"
                            }
                          >
                            {tutorial.metadata.difficulty}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {formatDate(tutorial.createdAt)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={tutorial.isActive ? "outline" : "secondary"}
                          >
                            {tutorial.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                className="h-8 w-8 p-0"
                              >
                                <span className="sr-only">Open menu</span>
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => router.push(`/tutorials/${tutorial.id}`)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => router.push(`/admin/tutorials/edit/${tutorial.id}`)}
                              >
                                <Edit className="h-4 w-4 mr-2" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive"
                                onClick={() => handleDelete(tutorial.id)}
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
