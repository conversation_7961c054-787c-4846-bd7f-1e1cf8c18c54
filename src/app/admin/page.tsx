"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import AdminLayout from "@/components/admin/AdminLayout";
import AdminDashboard from "@/components/dashboard/AdminDashboard";
import { ADMIN_CONFIG } from "@/lib/constants";

export default function AdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  React.useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (
      status === "authenticated" &&
      !ADMIN_CONFIG.permissions.allowedRoles.includes(
        (session?.user as any)?.role,
      )
    ) {
      router.push("/dashboard");
    }
  }, [status, session, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (
    !session ||
    !ADMIN_CONFIG.permissions.allowedRoles.includes(
      (session?.user as any)?.role,
    )
  ) {
    return null;
  }

  return (
    <AdminLayout
      title="Admin Dashboard"
      description="Monitor system performance, manage users, and track analytics."
    >
      <AdminDashboard />
    </AdminLayout>
  );
}
