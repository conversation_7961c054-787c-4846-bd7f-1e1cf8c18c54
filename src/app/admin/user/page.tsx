"use client";

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from "@/components/ui/use-toast";
import AdminLayout from "@/components/admin/AdminLayout";
import DataView from "@/components/admin/DataView";
import UserManagementDialog from "@/components/admin/UserManagementDialog";
import PasswordUpdateDialog from "@/components/admin/PasswordUpdateDialog";
import { ADMIN_CONFIG, TOAST_CONFIG } from "@/lib/constants";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  subscription: string;
  isActive: boolean;
  lastLoginAt: string | null;
  createdAt: string;
  updatedAt: string;
  _count?: {
    tutorials: number;
    aiUsage: number;
  };
}

export default function AdminUsersPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userDialog, setUserDialog] = useState<{
    open: boolean;
    mode: "create" | "edit";
    user?: User;
  }>({ open: false, mode: "create" });
  const [passwordDialog, setPasswordDialog] = useState<{
    open: boolean;
    userId: string;
    userName: string;
  }>({ open: false, userId: "", userName: "" });

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/users");

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setUsers(data.users || []);

      toast({
        title: "Users loaded",
        description: `Successfully loaded ${data.users?.length || 0} users.`,
        variant: TOAST_CONFIG.variants.success,
      });
    } catch (error) {
      console.error("Error fetching users:", error);
      toast({
        title: "Error loading users",
        description: "Failed to load user data. Please try again.",
        variant: TOAST_CONFIG.variants.error,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserAction = async (action: string, user: User) => {
    switch (action) {
      case "view":
        toast({
          title: "View User",
          description: `Viewing details for ${user.name}`,
          variant: TOAST_CONFIG.variants.info,
        });
        break;
      case "edit":
        setUserDialog({ open: true, mode: "edit", user });
        break;
      case "password":
        setPasswordDialog({
          open: true,
          userId: user.id,
          userName: user.name,
        });
        break;
      case "suspend":
        await handleToggleUserStatus(user);
        break;
      case "delete":
        await handleDeleteUser(user);
        break;
      default:
        break;
    }
  };

  const handleToggleUserStatus = async (user: User) => {
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isActive: !user.isActive,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to update user status");
      }

      toast({
        title: user.isActive ? "User Deactivated" : "User Activated",
        description: `${user.name} has been ${user.isActive ? "deactivated" : "activated"}.`,
        variant: TOAST_CONFIG.variants.success,
      });

      fetchUsers();
    } catch (error) {
      console.error("Error updating user status:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to update user status",
        variant: TOAST_CONFIG.variants.error,
      });
    }
  };

  const handleDeleteUser = async (user: User) => {
    if (
      !confirm(
        `Are you sure you want to delete ${user.name}? This action cannot be undone.`,
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to delete user");
      }

      toast({
        title: "User Deleted",
        description: `${user.name} has been deleted successfully.`,
        variant: TOAST_CONFIG.variants.success,
      });

      fetchUsers();
    } catch (error) {
      console.error("Error deleting user:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to delete user",
        variant: TOAST_CONFIG.variants.error,
      });
    }
  };

  const handleAddUser = () => {
    setUserDialog({ open: true, mode: "create" });
  };

  const handleUserDialogSuccess = () => {
    fetchUsers();
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? "default" : "secondary";
  };

  return (
    <AdminLayout>
      <div>
        <DataView title="Users" addButtonLabel="Add User" />

        <UserManagementDialog
          open={userDialog.open}
          onOpenChange={(open) => setUserDialog({ ...userDialog, open })}
          user={userDialog.user}
          mode={userDialog.mode}
          onSuccess={handleUserDialogSuccess}
        />

        <PasswordUpdateDialog
          open={passwordDialog.open}
          onOpenChange={(open) =>
            setPasswordDialog({ ...passwordDialog, open })
          }
          userId={passwordDialog.userId}
          userName={passwordDialog.userName}
        />
      </div>
    </AdminLayout>
  );
}
