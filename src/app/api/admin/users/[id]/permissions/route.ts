import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { prisma } from "@/lib/database";
import { canUserPerformAction, logAuditEvent } from "@/lib/permissions";
import { assignPermissionSchema } from "@/lib/validation";

// GET /api/admin/users/[id]/permissions - Get user permissions
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canRead = await canUserPerformAction(
      (session.user as any).id,
      "USER_READ",
    );
    if (!canRead) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Get user permissions
    const permissions = await prisma.userPermission.findMany({
      where: {
        userId: params.id,
        OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
      },
      select: {
        id: true,
        permission: true,
        grantedAt: true,
        expiresAt: true,
        granter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { grantedAt: "desc" },
    });

    return NextResponse.json({ permissions });
  } catch (error) {
    console.error("Error fetching user permissions:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}

// POST /api/admin/users/[id]/permissions - Assign permission to user
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canWrite = await canUserPerformAction(
      (session.user as any).id,
      "USER_WRITE",
    );
    if (!canWrite) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = assignPermissionSchema.safeParse({
      ...body,
      userId: params.id,
    });
    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const { permission, expiresAt } = validationResult.data;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true, name: true, email: true },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Check if permission already exists
    const existingPermission = await prisma.userPermission.findUnique({
      where: {
        userId_permission: {
          userId: params.id,
          permission: permission as any,
        },
      },
    });

    if (existingPermission) {
      return NextResponse.json(
        { message: "Permission already assigned to user" },
        { status: 409 },
      );
    }

    // Create permission
    const newPermission = await prisma.userPermission.create({
      data: {
        userId: params.id,
        permission: permission as any,
        grantedBy: (session.user as any).id,
        expiresAt,
      },
      include: {
        granter: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "permission_granted",
      resource: "user_permission",
      resourceId: newPermission.id,
      newValues: {
        userId: params.id,
        permission,
        expiresAt,
      },
      metadata: {
        targetUser: user,
      },
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json(
      {
        message: "Permission assigned successfully",
        permission: newPermission,
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error assigning permission:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/users/[id]/permissions/[permissionId] - Remove permission from user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canWrite = await canUserPerformAction(
      (session.user as any).id,
      "USER_WRITE",
    );
    if (!canWrite) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const url = new URL(request.url);
    const permissionId = url.searchParams.get("permissionId");

    if (!permissionId) {
      return NextResponse.json(
        { message: "Permission ID is required" },
        { status: 400 },
      );
    }

    // Get permission data for audit log
    const permission = await prisma.userPermission.findUnique({
      where: { id: permissionId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!permission) {
      return NextResponse.json(
        { message: "Permission not found" },
        { status: 404 },
      );
    }

    if (permission.userId !== params.id) {
      return NextResponse.json(
        { message: "Permission does not belong to this user" },
        { status: 400 },
      );
    }

    // Delete permission
    await prisma.userPermission.delete({
      where: { id: permissionId },
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "permission_revoked",
      resource: "user_permission",
      resourceId: permissionId,
      oldValues: {
        userId: permission.userId,
        permission: permission.permission,
        expiresAt: permission.expiresAt,
      },
      metadata: {
        targetUser: permission.user,
      },
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({
      message: "Permission removed successfully",
    });
  } catch (error) {
    console.error("Error removing permission:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}
