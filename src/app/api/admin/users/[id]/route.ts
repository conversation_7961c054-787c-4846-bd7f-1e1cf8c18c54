import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { prisma } from "@/lib/database";
import { canUserPerformAction, logAuditEvent } from "@/lib/permissions";
import { z } from "zod";

const updateUserSchema = z.object({
  name: z.string().min(2).max(50).optional(),
  role: z.enum(["USER", "MODERATOR", "ADMIN"]).optional(),
  isActive: z.boolean().optional(),
  subscription: z.string().optional(),
});

// GET /api/admin/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canRead = await canUserPerformAction(
      (session.user as any).id,
      "USER_READ",
    );
    if (!canRead) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        subscription: true,
        settings: true,
        lastLoginAt: true,
        loginAttempts: true,
        lockedUntil: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            tutorials: true,
            aiUsage: true,
            analytics: true,
          },
        },
        permissions: {
          select: {
            permission: true,
            grantedAt: true,
            expiresAt: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "user_viewed",
      resource: "user",
      resourceId: params.id,
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}

// PUT /api/admin/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canWrite = await canUserPerformAction(
      (session.user as any).id,
      "USER_WRITE",
    );
    if (!canWrite) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Get current user data for audit log
    const currentUser = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        role: true,
        isActive: true,
        subscription: true,
      },
    });

    if (!currentUser) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = updateUserSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const updateData = validationResult.data;

    // For updates, check if email is being changed and if it conflicts
    if (body.email && body.email !== currentUser.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: body.email },
        select: { id: true },
      });

      if (existingUser) {
        return NextResponse.json(
          { message: "User with this email already exists" },
          { status: 409 },
        );
      }
    }

    // Prevent self-deactivation or role change
    if (params.id === (session.user as any).id) {
      if (updateData.isActive === false) {
        return NextResponse.json(
          { message: "Cannot deactivate your own account" },
          { status: 400 },
        );
      }
      if (updateData.role && updateData.role !== currentUser.role) {
        return NextResponse.json(
          { message: "Cannot change your own role" },
          { status: 400 },
        );
      }
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isActive: true,
        subscription: true,
        updatedAt: true,
      },
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "user_updated",
      resource: "user",
      resourceId: params.id,
      oldValues: currentUser,
      newValues: updatedUser,
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({
      message: "User updated successfully",
      user: updatedUser,
    });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}

// DELETE /api/admin/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canDelete = await canUserPerformAction(
      (session.user as any).id,
      "USER_DELETE",
    );
    if (!canDelete) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Prevent self-deletion
    if (params.id === (session.user as any).id) {
      return NextResponse.json(
        { message: "Cannot delete your own account" },
        { status: 400 },
      );
    }

    // Get user data for audit log
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Delete user (cascade will handle related records)
    await prisma.user.delete({
      where: { id: params.id },
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "user_deleted",
      resource: "user",
      resourceId: params.id,
      oldValues: user,
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({
      message: "User deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}
