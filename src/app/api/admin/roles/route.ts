import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { prisma } from "@/lib/database";
import {
  canUserPerformAction,
  logAuditEvent,
  getRolePermissions,
} from "@/lib/permissions";

// GET /api/admin/roles - Get all roles with their permissions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canRead = await canUserPerformAction(
      (session.user as any).id,
      "USER_READ",
    );
    if (!canRead) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Get role permissions from database
    const rolePermissions = await prisma.rolePermission.findMany({
      select: {
        role: true,
        permission: true,
      },
      orderBy: [{ role: "asc" }, { permission: "asc" }],
    });

    // Group permissions by role
    const rolesWithPermissions = rolePermissions.reduce(
      (acc, rp) => {
        if (!acc[rp.role]) {
          acc[rp.role] = {
            role: rp.role,
            permissions: [],
          };
        }
        acc[rp.role].permissions.push(rp.permission);
        return acc;
      },
      {} as Record<string, { role: string; permissions: string[] }>,
    );

    // Ensure all roles are included even if they have no custom permissions
    const allRoles = ["USER", "MODERATOR", "ADMIN", "SUPER_ADMIN"];
    const roles = allRoles.map((role) => ({
      role,
      permissions:
        rolesWithPermissions[role]?.permissions ||
        getRolePermissions(role as any),
      userCount: 0, // Will be populated separately if needed
    }));

    // Get user counts for each role
    const userCounts = await prisma.user.groupBy({
      by: ["role"],
      _count: {
        id: true,
      },
    });

    // Add user counts to roles
    roles.forEach((role) => {
      const count =
        userCounts.find((uc) => uc.role === role.role)?._count.id || 0;
      role.userCount = count;
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "roles_listed",
      resource: "role",
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({ roles });
  } catch (error) {
    console.error("Error fetching roles:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}
