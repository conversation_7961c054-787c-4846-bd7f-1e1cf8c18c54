import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database";
import { z } from "zod";
import crypto from "crypto";
import { AUTH_CONFIG } from "@/lib/config";
import { rateLimit } from "@/lib/rate-limit";
import { sendPasswordResetEmail } from "@/lib/email";

// Rate limiting for password reset requests
const resetLimiter = rateLimit({
  interval: 60 * 60 * 1000, // 1 hour
  uniqueTokenPerInterval: 500,
});

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const ip =
      request.ip ?? request.headers.get("x-forwarded-for") ?? "unknown";
    const { success, limit, reset, remaining } = await resetLimiter.check(
      3,
      ip,
    );

    if (!success) {
      return NextResponse.json(
        {
          error: "Too many password reset attempts. Please try again later.",
          retryAfter: Math.round((reset - Date.now()) / 1000),
        },
        {
          status: 429,
          headers: {
            "X-RateLimit-Limit": limit.toString(),
            "X-RateLimit-Remaining": remaining.toString(),
            "X-RateLimit-Reset": reset.toString(),
          },
        },
      );
    }

    const body = await request.json();
    const validationResult = forgotPasswordSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.flatten().fieldErrors },
        { status: 400 },
      );
    }

    const { email } = validationResult.data;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      // For security reasons, don't reveal that the user doesn't exist
      // Return success even though no email will be sent
      return NextResponse.json({
        success: true,
        message: "If your email is registered, you will receive a reset link",
      });
    }

    // Generate a secure random token
    const token = crypto.randomBytes(32).toString("hex");
    const expires = new Date(Date.now() + 3600000); // 1 hour from now

    // Store the token in the database
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token,
        expires,
      },
    });

    // Send password reset email
    const resetLink = `${AUTH_CONFIG.NEXTAUTH_URL}/auth/reset-password?token=${token}`;

    try {
      await sendPasswordResetEmail(user.email, user.name || "User", resetLink);
    } catch (emailError) {
      console.error("Failed to send password reset email:", emailError);
      // Don't reveal email sending failure to prevent enumeration
    }

    // Log the password reset request
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: "password_reset_requested",
        resource: "user",
        resourceId: user.id,
        metadata: {
          timestamp: new Date(),
          ipAddress: request.headers.get("x-forwarded-for") || "unknown",
          userAgent: request.headers.get("user-agent") || "unknown",
        },
      },
    });

    // Always return success to prevent email enumeration
    const response = {
      success: true,
      message:
        "If your email is registered, you will receive a password reset link",
    };

    // In development, include the reset link for testing
    if (process.env.NODE_ENV === "development") {
      (response as any).devInfo = { resetLink };
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error requesting password reset:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
