import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { PrismaAdapter } from "@next-auth/prisma-adapter";
import { prisma } from "@/lib/database";
import { defaultUserSettings, UserSettings } from "@/models/User";
import bcrypt from "bcryptjs";
import { AUTH_CONFIG } from "@/lib/config";
import {
  incrementLoginAttempts,
  resetLoginAttempts,
  isAccountLocked,
  lockUserAccount,
} from "@/lib/permissions";

// Validate required environment variables
if (!process.env.NEXTAUTH_SECRET) {
  throw new Error("NEXTAUTH_SECRET environment variable is required");
}

if (!process.env.NEXTAUTH_URL) {
  throw new Error("NEXTAUTH_URL environment variable is required");
}

// Create a secure adapter with proper error handling
const createSecureAdapter = () => {
  const adapter = PrismaAdapter(prisma);

  return {
    ...adapter,
    createUser: async (data: any) => {
      try {
        const user = await adapter.createUser?.({
          ...data,
          settings: defaultUserSettings,
          isActive: true,
          loginAttempts: 0,
        });
        return user;
      } catch (error) {
        console.error("Error creating user:", error);
        throw error;
      }
    },
    getUser: async (id: string) => {
      try {
        return (await adapter.getUser?.(id)) || null;
      } catch (error) {
        console.error("Error getting user:", error);
        return null;
      }
    },
    getUserByEmail: async (email: string) => {
      try {
        return (await adapter.getUserByEmail?.(email)) || null;
      } catch (error) {
        console.error("Error getting user by email:", error);
        return null;
      }
    },
  };
};

const handler = NextAuth({
  adapter: createSecureAdapter(),
  providers: [
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
      ? [
          GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          }),
        ]
      : []),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: credentials.email.toLowerCase() },
            select: {
              id: true,
              email: true,
              name: true,
              image: true,
              password: true,
              role: true,
              isActive: true,
              loginAttempts: true,
              lockedUntil: true,
            },
          });

          if (!user || !user.password || !user.isActive) {
            return null;
          }

          // Check if account is locked
          if (await isAccountLocked(user.id)) {
            throw new Error(
              "Account is temporarily locked due to multiple failed login attempts",
            );
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password,
          );

          if (!isPasswordValid) {
            // Increment failed login attempts
            const attempts = await incrementLoginAttempts(user.id);

            // Lock account after 5 failed attempts
            if (attempts >= 5) {
              await lockUserAccount(user.id, 30); // Lock for 30 minutes
              throw new Error(
                "Account locked due to multiple failed login attempts",
              );
            }

            return null;
          }

          // Reset login attempts on successful login
          await resetLoginAttempts(user.id);

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role,
          };
        } catch (error) {
          console.error("Error during authentication:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async session({ session, user }) {
      if (session?.user) {
        try {
          // Get user settings from database
          const userData = await prisma.user.findUnique({
            where: { id: user.id },
            select: { role: true, subscription: true, settings: true },
          });

          (session.user as any).role = userData?.role || "USER";
          (session.user as any).subscription = userData?.subscription || "free";
          (session.user as any).settings =
            userData?.settings || (defaultUserSettings as UserSettings);
          (session.user as any).id = user.id;
        } catch (error) {
          console.error("Error fetching user data for session:", error);
          // Provide default values if database query fails
          (session.user as any).role = "USER";
          (session.user as any).subscription = "free";
          (session.user as any).settings = defaultUserSettings as UserSettings;
          (session.user as any).id = user.id;
        }
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          // Check if user exists
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! },
          });

          if (!existingUser) {
            // Create new user with default settings
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name,
                image: user.image,
                settings: defaultUserSettings as any,
              },
            });
          }
          return true;
        } catch (error) {
          console.error("Error during sign in:", error);
          // Allow sign-in even if database operations fail
          return true;
        }
      }
      return true;
    },
    async redirect({ url, baseUrl }) {
      // Redirect to dashboard after successful login
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return `${baseUrl}/dashboard`;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "database",
    maxAge: AUTH_CONFIG.session.maxAge,
    updateAge: AUTH_CONFIG.session.updateAge,
  },
  jwt: {
    maxAge: AUTH_CONFIG.jwt.maxAge,
  },
});

export { handler as GET, handler as POST };
