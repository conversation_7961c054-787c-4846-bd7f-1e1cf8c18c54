import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { UpdateTutorialData } from "@/models/Tutorial";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const tutorialId = params.id;

    // Get user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get tutorial
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Get user progress
    const progress = await prisma.$queryRaw`
      SELECT 
        "tutorialId",
        "userId",
        "currentStep",
        "completed",
        "startedAt",
        "completedAt",
        "timeSpent",
        "progress"
      FROM "TutorialProgress"
      WHERE "tutorialId" = ${tutorialId} AND "userId" = ${user.id}
    `;

    // Update view count
    await prisma.$executeRaw`
      UPDATE "Tutorial"
      SET "metadata" = jsonb_set(
        "metadata", 
        '{analytics,views}', 
        (COALESCE(("metadata"->'analytics'->>'views')::int, 0) + 1)::text::jsonb
      )
      WHERE "id" = ${tutorialId}
    `;

    // Log view analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_viewed",
        metadata: {
          timestamp: new Date(),
        },
      },
    });

    return NextResponse.json({
      tutorial,
      userProgress: progress[0] || null,
    });
  } catch (error) {
    console.error("Error fetching tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, role: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if tutorial exists and user has permission
    const existingTutorial = await prisma.tutorial.findUnique({
      where: { id: params.id },
      select: { createdBy: true },
    });

    if (!existingTutorial) {
      return NextResponse.json(
        { error: "Tutorial not found" },
        { status: 404 },
      );
    }

    // Check permissions
    if (
      existingTutorial.createdBy !== user.id &&
      user.role !== "ADMIN" &&
      user.role !== "MODERATOR"
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const updateData: UpdateTutorialData = body;

    // Update tutorial
    const tutorial = await prisma.tutorial.update({
      where: { id: params.id },
      data: {
        ...(updateData.title && { title: updateData.title }),
        ...(updateData.description && { description: updateData.description }),
        ...(updateData.steps && {
          steps: updateData.steps.map((step, index) => ({
            ...step,
            id: step.id || `step-${index + 1}`,
          })),
        }),
        ...(updateData.language && { language: updateData.language }),
        ...(updateData.isActive !== undefined && {
          isActive: updateData.isActive,
        }),
        ...(updateData.metadata && {
          metadata: {
            ...updateData.metadata,
            analytics: existingTutorial.metadata?.analytics || {
              views: 0,
              completions: 0,
              averageRating: 0,
              feedback: [],
            },
          },
        }),
      },
    });

    // Track tutorial update
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId: tutorial.id,
        action: "tutorial_updated",
        metadata: {
          title: tutorial.title,
          updatedFields: Object.keys(updateData),
        },
      },
    });

    return NextResponse.json({
      message: "Tutorial updated successfully",
      tutorial,
    });
  } catch (error) {
    console.error("Error updating tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const tutorialId = params.id;

    // Get user role
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, role: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has permission to delete tutorials
    if (user.role !== "ADMIN" && user.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Check if tutorial exists
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Delete tutorial progress
    await prisma.$executeRaw`
      DELETE FROM "TutorialProgress"
      WHERE "tutorialId" = ${tutorialId}
    `;

    // Delete analytics related to this tutorial
    await prisma.$executeRaw`
      DELETE FROM "Analytics"
      WHERE "tutorialId" = ${tutorialId}
    `;

    // Delete tutorial
    await prisma.tutorial.delete({
      where: { id: tutorialId },
    });

    // Log deletion
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: "tutorial_deleted",
        resource: "tutorial",
        resourceId: tutorialId,
        metadata: {
          tutorialTitle: tutorial.title,
          timestamp: new Date(),
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "Tutorial deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
