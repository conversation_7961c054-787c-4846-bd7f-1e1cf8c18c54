import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { z } from "zod";

const progressSchema = z.object({
  progress: z.number().min(0).max(100),
  completed: z.boolean().optional(),
  currentStep: z.number().min(0).optional(),
  timeSpent: z.number().min(0).optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const tutorialId = params.id;

    // Get user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if tutorial exists
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Get user progress
    const progress = await prisma.$queryRaw`
      SELECT 
        "tutorialId",
        "userId",
        "currentStep",
        "completed",
        "startedAt",
        "completedAt",
        "timeSpent"
      FROM "TutorialProgress"
      WHERE "tutorialId" = ${tutorialId} AND "userId" = ${user.id}
    `;

    return NextResponse.json({
      progress: progress[0] || null,
    });
  } catch (error) {
    console.error("Error fetching tutorial progress:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const tutorialId = params.id;
    const body = await request.json();
    
    // Validate request body
    const validationResult = progressSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.flatten() },
        { status: 400 }
      );
    }
    
    const { progress, completed, currentStep, timeSpent } = validationResult.data;

    // Get user ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if tutorial exists
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check if progress record exists
    const existingProgress = await prisma.$queryRaw`
      SELECT id FROM "TutorialProgress"
      WHERE "tutorialId" = ${tutorialId} AND "userId" = ${user.id}
    `;

    const now = new Date();
    
    if (existingProgress.length === 0) {
      // Create new progress record
      await prisma.$executeRaw`
        INSERT INTO "TutorialProgress" (
          "tutorialId", 
          "userId", 
          "currentStep", 
          "completed", 
          "startedAt", 
          "completedAt", 
          "timeSpent",
          "progress"
        )
        VALUES (
          ${tutorialId},
          ${user.id},
          ${currentStep || 0},
          ${completed || false},
          ${now},
          ${completed ? now : null},
          ${timeSpent || 0},
          ${progress}
        )
      `;
    } else {
      // Update existing progress record
      await prisma.$executeRaw`
        UPDATE "TutorialProgress"
        SET 
          "currentStep" = ${currentStep !== undefined ? currentStep : prisma.raw("\"currentStep\"")},
          "completed" = ${completed !== undefined ? completed : prisma.raw("\"completed\"")},
          "completedAt" = ${completed ? now : prisma.raw("\"completedAt\"")},
          "timeSpent" = ${timeSpent !== undefined ? timeSpent : prisma.raw("\"timeSpent\"")},
          "progress" = ${progress}
        WHERE "tutorialId" = ${tutorialId} AND "userId" = ${user.id}
      `;
    }

    // If completed, update analytics
    if (completed) {
      await prisma.$executeRaw`
        UPDATE "Tutorial"
        SET "metadata" = jsonb_set(
          jsonb_set(
            "metadata", 
            '{analytics,completions}', 
            (COALESCE(("metadata"->'analytics'->>'completions')::int, 0) + 1)::text::jsonb
          ),
          '{analytics,feedback}',
          COALESCE("metadata"->'analytics'->'feedback', '[]'::jsonb)
        )
        WHERE "id" = ${tutorialId}
      `;

      // Log analytics event
      await prisma.analytics.create({
        data: {
          userId: user.id,
          tutorialId,
          action: "tutorial_completed",
          metadata: {
            completedAt: now,
            timeSpent: timeSpent || 0,
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: "Progress updated successfully",
    });
  } catch (error) {
    console.error("Error updating tutorial progress:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 