import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { CreateTutorialData } from "@/models/Tutorial";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const category = searchParams.get("category");
    const difficulty = searchParams.get("difficulty");
    const language = searchParams.get("language") || "en";

    const skip = (page - 1) * limit;

    const where: any = {
      isActive: true,
      language,
    };

    if (category) {
      where.metadata = {
        path: ["category"],
        equals: category,
      };
    }

    if (difficulty) {
      where.metadata = {
        ...where.metadata,
        path: ["difficulty"],
        equals: difficulty,
      };
    }

    const [tutorials, total] = await Promise.all([
      prisma.tutorial.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: "desc",
        },
        select: {
          id: true,
          title: true,
          description: true,
          language: true,
          metadata: true,
          createdAt: true,
          updatedAt: true,
          createdBy: true,
        },
      }),
      prisma.tutorial.count({ where }),
    ]);

    return NextResponse.json({
      tutorials,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching tutorials:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, role: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has permission to create tutorials
    if (user.role !== "ADMIN" && user.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const tutorialData: CreateTutorialData = body;

    // Validate required fields
    if (
      !tutorialData.title ||
      !tutorialData.steps ||
      tutorialData.steps.length === 0
    ) {
      return NextResponse.json(
        { error: "Title and steps are required" },
        { status: 400 },
      );
    }

    // Create tutorial
    const tutorial = await prisma.tutorial.create({
      data: {
        title: tutorialData.title,
        description: tutorialData.description,
        steps: tutorialData.steps.map((step, index) => ({
          ...step,
          id: `step-${index + 1}`,
        })),
        language: tutorialData.language || "en",
        metadata: {
          ...tutorialData.metadata,
          analytics: {
            views: 0,
            completions: 0,
            averageRating: 0,
            feedback: [],
          },
        },
        createdBy: user.id,
      },
    });

    // Track tutorial creation
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId: tutorial.id,
        action: "tutorial_created",
        metadata: {
          title: tutorial.title,
          stepsCount: tutorialData.steps.length,
          category: tutorialData.metadata.category,
          difficulty: tutorialData.metadata.difficulty,
        },
      },
    });

    return NextResponse.json(
      {
        message: "Tutorial created successfully",
        tutorial: {
          id: tutorial.id,
          title: tutorial.title,
          description: tutorial.description,
          language: tutorial.language,
          metadata: tutorial.metadata,
          createdAt: tutorial.createdAt,
        },
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("Error creating tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
