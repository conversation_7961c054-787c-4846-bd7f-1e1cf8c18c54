/**
 * AG-UI Message Endpoint
 * Handles incoming messages and triggers AI agent responses
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAgUIServer } from '@/lib/ag-ui-server';
import { z } from 'zod';

const MessageSchema = z.object({
  message: z.string(),
  context: z.object({
    pageUrl: z.string().optional(),
    pageTitle: z.string().optional(),
    userQuery: z.string().optional(),
    selector: z.string().optional(),
    question: z.string().optional(),
    domElements: z.array(z.object({
      selector: z.string(),
      text: z.string(),
      type: z.string()
    })).optional(),
    userPreferences: z.record(z.any()).optional()
  }).optional(),
  timestamp: z.string()
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, context, timestamp } = MessageSchema.parse(body);
    
    const agUIServer = getAgUIServer();
    const runId = `run_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const threadId = `thread_${Date.now()}`;

    // Route message based on type
    switch (message) {
      case 'generate_tutorial':
        if (!context?.pageUrl) {
          return NextResponse.json(
            { error: 'pageUrl is required for tutorial generation' },
            { status: 400 }
          );
        }
        
        await agUIServer.generateTutorial({
          pageUrl: context.pageUrl,
          pageTitle: context.pageTitle,
          userQuery: context.userQuery,
          domElements: context.domElements
        }, runId, threadId);
        break;

      case 'explain_element':
        if (!context?.selector) {
          return NextResponse.json(
            { error: 'selector is required for element explanation' },
            { status: 400 }
          );
        }
        
        await agUIServer.explainElement(
          context.selector,
          context.question,
          context.pageUrl || window?.location?.href || 'unknown',
          runId,
          threadId
        );
        break;

      default:
        // Handle general chat messages
        await agUIServer.explainElement(
          'body', // Default to body element
          message,
          context?.pageUrl || 'unknown',
          runId,
          threadId
        );
        break;
    }

    return NextResponse.json({
      success: true,
      runId,
      threadId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('AG-UI message error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}
