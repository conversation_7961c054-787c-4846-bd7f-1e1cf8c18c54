import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { AIProviderConfig } from "@/models/AIUsage";
import { prisma } from "@/lib/database";

const AI_PROVIDERS: AIProviderConfig[] = [
  {
    provider: "openai",
    name: "OpenAI",
    isActive: true,
    models: [
      {
        id: "gpt-4-turbo-preview",
        name: "GPT-4 Turbo",
        costPer1kTokens: 0.01,
        maxTokens: 128000,
      },
      {
        id: "gpt-3.5-turbo",
        name: "GPT-3.5 Turbo",
        costPer1kTokens: 0.002,
        maxTokens: 16385,
      },
    ],
    settings: {
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
  },
  {
    provider: "anthropic",
    name: "Anthropic",
    isActive: true,
    models: [
      {
        id: "claude-3-sonnet-20240229",
        name: "Claude 3 Sonnet",
        costPer1kTokens: 0.003,
        maxTokens: 200000,
      },
      {
        id: "claude-3-haiku-20240307",
        name: "Claude 3 Haiku",
        costPer1kTokens: 0.00025,
        maxTokens: 200000,
      },
    ],
    settings: {
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
  },
  {
    provider: "google",
    name: "Google AI",
    isActive: false,
    models: [
      {
        id: "gemini-pro",
        name: "Gemini Pro",
        costPer1kTokens: 0.001,
        maxTokens: 32768,
      },
    ],
    settings: {
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
  },
  {
    provider: "openrouter",
    name: "OpenRouter",
    isActive: false,
    models: [
      {
        id: "openai/gpt-4-turbo-preview",
        name: "GPT-4 Turbo (OpenRouter)",
        costPer1kTokens: 0.01,
        maxTokens: 128000,
      },
    ],
    settings: {
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
  },
  {
    provider: "groq",
    name: "Groq",
    isActive: false,
    models: [
      {
        id: "mixtral-8x7b-32768",
        name: "Mixtral 8x7B",
        costPer1kTokens: 0.0005,
        maxTokens: 32768,
      },
    ],
    settings: {
      temperature: 0.7,
      maxTokens: 2000,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
    },
  },
];

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get("active") === "true";

    let providers = AI_PROVIDERS;

    if (activeOnly) {
      providers = providers.filter((p) => p.isActive);
    }

    // Remove sensitive information like API keys
    const sanitizedProviders = providers.map(
      ({ apiKey, ...provider }) => provider,
    );

    return NextResponse.json({ providers: sanitizedProviders });
  } catch (error) {
    console.error("Error fetching AI providers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { role: true },
    });

    if (user?.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const { provider, isActive, settings } = body;

    // In a real implementation, you would update the provider configuration
    // This is a mock response
    return NextResponse.json({
      message: "Provider configuration updated",
      provider,
      isActive,
      settings,
    });
  } catch (error) {
    console.error("Error updating AI provider:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
