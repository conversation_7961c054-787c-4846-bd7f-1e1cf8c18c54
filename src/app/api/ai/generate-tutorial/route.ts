import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { CreateTutorialData, TutorialStep } from "@/models/Tutorial";
import { AIProvider } from "@/models/AIUsage";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const body = await request.json();
    const {
      url,
      objective,
      difficulty = "beginner",
      language = "en",
      provider = (user.settings as any)?.aiProvider || "openai",
    } = body;

    if (!url || !objective) {
      return NextResponse.json(
        { error: "URL and objective are required" },
        { status: 400 },
      );
    }

    const startTime = Date.now();

    // Generate tutorial using AI
    const tutorialData = await generateTutorialWithAI({
      url,
      objective,
      difficulty,
      language,
      provider: provider as AIProvider,
    });

    // Create tutorial in database
    const tutorial = await prisma.tutorial.create({
      data: {
        title: tutorialData.title,
        description: tutorialData.description,
        steps: tutorialData.steps,
        language,
        createdBy: user.id,
        metadata: {
          ...tutorialData.metadata,
          analytics: {
            views: 0,
            completions: 0,
            averageRating: 0,
            feedback: [],
          },
        },
      },
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Track AI usage
    await prisma.aIUsage.create({
      data: {
        userId: user.id,
        provider: provider as AIProvider,
        model: "gpt-4-turbo-preview", // This would be dynamic based on provider
        tokens: Math.ceil(((objective.length + url.length) / 4) * 10), // Rough estimation
        cost: calculateTutorialGenerationCost(
          provider as AIProvider,
          objective.length,
        ),
        requestType: "tutorial_generation",
        metadata: {
          duration,
          success: true,
          language,
          tutorialId: tutorial.id,
          prompt: `Generate tutorial for ${objective} on ${url}`,
          customData: { difficulty, stepsGenerated: tutorialData.steps.length },
        },
      },
    });

    return NextResponse.json({
      tutorial: {
        id: tutorial.id,
        title: tutorial.title,
        description: tutorial.description,
        steps: tutorial.steps,
        language: tutorial.language,
        metadata: tutorial.metadata,
        createdAt: tutorial.createdAt,
      },
    });
  } catch (error) {
    console.error("Error generating tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

async function generateTutorialWithAI(params: {
  url: string;
  objective: string;
  difficulty: string;
  language: string;
  provider: AIProvider;
}): Promise<CreateTutorialData> {
  // Mock AI tutorial generation
  // In a real implementation, this would call the actual AI provider

  const { url, objective, difficulty, language } = params;

  const steps: Omit<TutorialStep, "id">[] = [
    {
      title: "Getting Started",
      description: "Welcome to this tutorial",
      action: "explain",
      content: `Welcome! This tutorial will guide you through ${objective} on ${url}. Let's begin by understanding the interface.`,
      voiceContent: `Welcome to this interactive tutorial. I'll guide you step by step to help you ${objective}.`,
      position: { x: 100, y: 100 },
      duration: 3000,
    },
    {
      title: "Navigate to Main Section",
      description: "Find and click on the main navigation",
      selector: "nav, .navigation, .menu",
      action: "click",
      content:
        "Look for the main navigation menu, usually located at the top of the page.",
      voiceContent:
        "First, let's locate the main navigation menu. It's typically found at the top of the page.",
      position: { x: 200, y: 50 },
      duration: 5000,
    },
    {
      title: "Complete the Objective",
      description: "Follow the highlighted elements to complete your goal",
      action: "explain",
      content: `Now you're ready to ${objective}. Follow the highlighted elements and instructions.`,
      voiceContent: `Great! Now you have all the tools you need to ${objective}. Take your time and follow along.`,
      position: { x: 300, y: 200 },
      duration: 8000,
    },
    {
      title: "Tutorial Complete",
      description: "Congratulations on completing the tutorial!",
      action: "explain",
      content:
        "Excellent work! You've successfully completed this tutorial. You can now apply what you've learned.",
      voiceContent:
        "Congratulations! You've successfully completed this tutorial. Well done!",
      position: { x: 400, y: 300 },
      duration: 3000,
    },
  ];

  return {
    title: `How to ${objective}`,
    description: `A step-by-step tutorial to help you ${objective} on ${url}`,
    steps,
    language,
    metadata: {
      category: "web-navigation",
      difficulty: difficulty as "beginner" | "intermediate" | "advanced",
      estimatedTime:
        steps.reduce((total, step) => total + (step.duration || 0), 0) / 1000,
      tags: [
        "tutorial",
        "web-navigation",
        objective.toLowerCase().replace(/\s+/g, "-"),
      ],
      targetUrl: url,
      version: "1.0",
    },
  };
}

function calculateTutorialGenerationCost(
  provider: AIProvider,
  textLength: number,
): number {
  const baseCosts = {
    openai: 0.01,
    anthropic: 0.015,
    google: 0.005,
    openrouter: 0.01,
    groq: 0.005,
  };

  // Tutorial generation is more complex, so higher cost multiplier
  const tokens = Math.ceil((textLength / 4) * 10);
  return (tokens / 1000) * baseCosts[provider];
}
