import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AnalyticsDashboard } from "@/models/Analytics";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, role: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user has admin access
    if (user.role !== "ADMIN" && user.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "month";

    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case "day":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "year":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get total users
    const totalUsers = await prisma.user.count();

    // Get active users (users who have activity in the period)
    const activeUsers = await prisma.analytics.groupBy({
      by: ["userId"],
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      _count: {
        userId: true,
      },
    });

    // Get total tutorials
    const totalTutorials = await prisma.tutorial.count({
      where: { isActive: true },
    });

    // Get tutorial statistics
    const tutorialStats = await prisma.analytics.groupBy({
      by: ["action"],
      where: {
        timestamp: {
          gte: startDate,
        },
        action: {
          in: ["tutorial_started", "tutorial_completed"],
        },
      },
      _count: {
        id: true,
      },
    });

    const tutorialStarted =
      tutorialStats.find((s: any) => s.action === "tutorial_started")?._count.id || 0;
    const tutorialCompleted =
      tutorialStats.find((s: any) => s.action === "tutorial_completed")?._count.id || 0;

    const completionRate =
      tutorialStarted > 0 ? (tutorialCompleted / tutorialStarted) * 100 : 0;

    // Get average session time (mock data for now)
    const averageSessionTime = 15; // minutes

    // Get top tutorials
    const topTutorials = await prisma.tutorial.findMany({
      where: { isActive: true },
      select: {
        id: true,
        title: true,
        metadata: true,
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5,
    });

    // Sort manually by views from metadata after fetching
    const sortedTutorials = topTutorials.sort((a, b) => {
      const aViews = (a.metadata as any)?.analytics?.views || 0;
      const bViews = (b.metadata as any)?.analytics?.views || 0;
      return bViews - aViews; // Sort by descending views
    });

    // Get user growth data (simplified)
    const userGrowth = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const users = await prisma.user.count({
        where: {
          createdAt: {
            lte: date,
          },
        },
      });
      userGrowth.push({
        date: date.toISOString().split("T")[0],
        users,
      });
    }

    // Get AI usage by provider
    const aiUsageByProvider = await prisma.aIUsage.groupBy({
      by: ["provider"],
      where: {
        timestamp: {
          gte: startDate,
        },
      },
      _count: {
        id: true,
      },
      _sum: {
        cost: true,
      },
    });

    const usageByProvider = aiUsageByProvider.map((usage: any) => ({
      provider: usage.provider,
      usage: usage._count.id,
      cost: usage._sum.cost || 0,
    }));

    const dashboard: AnalyticsDashboard = {
      totalUsers,
      activeUsers: activeUsers.length,
      totalTutorials,
      completionRate,
      averageSessionTime,
      topTutorials: sortedTutorials.map((tutorial: any) => ({
        id: tutorial.id,
        title: tutorial.title,
        views: tutorial.metadata?.analytics?.views || 0,
        completions: tutorial.metadata?.analytics?.completions || 0,
      })),
      userGrowth,
      usageByProvider,
    };

    return NextResponse.json({ dashboard });
  } catch (error) {
    console.error("Error fetching dashboard analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
