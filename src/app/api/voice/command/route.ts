import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { voiceCommandProcessor } from "@/lib/voice-command-processor";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const contentType = request.headers.get("content-type");
    let result;

    if (contentType?.includes("multipart/form-data")) {
      // Handle audio upload
      const formData = await request.formData();
      const audioFile = formData.get("audio") as File;
      const context = JSON.parse((formData.get("context") as string) || "{}");

      if (!audioFile) {
        return NextResponse.json(
          { error: "Audio file is required" },
          { status: 400 },
        );
      }

      const audioBlob = new Blob([await audioFile.arrayBuffer()], {
        type: audioFile.type,
      });

      result = await voiceCommandProcessor.processAudioCommand(
        audioBlob,
        user.id,
        context,
      );
    } else {
      // Handle text command
      const body = await request.json();
      const { text, context } = body;

      if (!text) {
        return NextResponse.json(
          { error: "Text is required" },
          { status: 400 },
        );
      }

      result = await voiceCommandProcessor.processTextCommand(
        text,
        user.id,
        context,
      );
    }

    // Track analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        action: "voice_command_api_call",
        metadata: {
          success: result.success,
          action: result.command.action,
          confidence: result.command.confidence,
          processingTime: Date.now() - result.command.timestamp.getTime(),
        },
      },
    });

    return NextResponse.json({
      success: result.success,
      command: {
        id: result.command.id,
        action: result.command.action,
        confidence: result.command.confidence,
        parameters: result.command.parameters,
      },
      response: result.response,
      error: result.error,
    });
  } catch (error) {
    console.error("Error processing voice command:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get command history
    const history = voiceCommandProcessor.getCommandHistory(user.id);

    // Get recent analytics
    const recentCommands = await prisma.analytics.findMany({
      where: {
        userId: user.id,
        action: "voice_command_processed",
        timestamp: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
      orderBy: { timestamp: "desc" },
      take: 50,
    });

    return NextResponse.json({
      history: history.slice(-20), // Last 20 commands
      recentCommands: recentCommands.map((cmd) => ({
        id: cmd.id,
        action: cmd.metadata.action,
        confidence: cmd.metadata.confidence,
        success: cmd.metadata.success,
        timestamp: cmd.timestamp,
      })),
      supportedActions: [
        "explain_element",
        "start_tutorial",
        "next_step",
        "previous_step",
        "stop_tutorial",
        "pause_tutorial",
        "resume_tutorial",
        "repeat_step",
        "click_element",
        "scroll",
      ],
    });
  } catch (error) {
    console.error("Error fetching voice command data:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
