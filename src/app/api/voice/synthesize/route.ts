import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";

// ElevenLabs API configuration
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1";
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true, settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const body = await request.json();
    const {
      text,
      voice = (user.settings as any)?.voiceSettings?.voice || "default",
      speed = (user.settings as any)?.voiceSettings?.speed || 1.0,
      language = (user.settings as any)?.language || "en",
    } = body;

    if (!text) {
      return NextResponse.json(
        { error: "Text is required" },
        { status: 400 }
      );
    }

    const startTime = Date.now();

    // If ElevenLabs API key is available, use real TTS
    if (ELEVENLABS_API_KEY) {
      try {
        const response = await fetch(
          `${ELEVENLABS_API_URL}/text-to-speech/${voice}`,
          {
            method: "POST",
            headers: {
              "Accept": "audio/mpeg",
              "Content-Type": "application/json",
              "xi-api-key": ELEVENLABS_API_KEY,
            },
            body: JSON.stringify({
              text,
              model_id: "eleven_monolingual_v1",
              voice_settings: {
                stability: 0.5,
                similarity_boost: 0.5,
                style: 0.0,
                use_speaker_boost: true,
              },
            }),
          }
        );

        if (response.ok) {
          const audioBuffer = await response.arrayBuffer();
          const endTime = Date.now();

          // Track voice usage
          await prisma.analytics.create({
            data: {
              userId: user.id,
              action: "voice_used",
              metadata: {
                textLength: text.length,
                voice,
                speed,
                language,
                duration: endTime - startTime,
                provider: "elevenlabs",
                success: true,
              },
            },
          });

          return new Response(audioBuffer, {
            headers: {
              "Content-Type": "audio/mpeg",
              "Content-Length": audioBuffer.byteLength.toString(),
            },
          });
        }
      } catch (error) {
        console.error("ElevenLabs API error:", error);
        // Fall back to browser TTS
      }
    }

    // Fallback: Return instructions for browser-based TTS
    const endTime = Date.now();

    // Track voice usage (fallback)
    await prisma.analytics.create({
      data: {
        userId: user.id,
        action: "voice_used",
        metadata: {
          textLength: text.length,
          voice,
          speed,
          language,
          duration: endTime - startTime,
          provider: "browser",
          success: true,
        },
      },
    });

    return NextResponse.json({
      message: "Using browser TTS",
      text,
      voice,
      speed,
      language,
      useBrowserTTS: true,
    });
  } catch (error) {
    console.error("Error in voice synthesis:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Return available voices
    const voices = [
      {
        id: "default",
        name: "Default Voice",
        language: "en",
        gender: "neutral",
        provider: "browser",
      },
      {
        id: "rachel",
        name: "Rachel",
        language: "en",
        gender: "female",
        provider: "elevenlabs",
        available: !!ELEVENLABS_API_KEY,
      },
      {
        id: "adam",
        name: "Adam",
        language: "en",
        gender: "male",
        provider: "elevenlabs",
        available: !!ELEVENLABS_API_KEY,
      },
      {
        id: "domi",
        name: "Domi",
        language: "en",
        gender: "female",
        provider: "elevenlabs",
        available: !!ELEVENLABS_API_KEY,
      },
    ];

    return NextResponse.json({ voices });
  } catch (error) {
    console.error("Error fetching voices:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
