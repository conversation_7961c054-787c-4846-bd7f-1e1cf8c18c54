import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { UserSettings } from "@/models/User";

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { settings } = body as { settings: Partial<UserSettings> };

    // Get current user settings
    const currentUser = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { settings: true },
    });

    if (!currentUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Merge with existing settings
    const updatedSettings = {
      ...currentUser.settings,
      ...settings,
    };

    const updatedUser = await prisma.user.update({
      where: { email: session.user.email },
      data: { settings: updatedSettings },
      select: {
        id: true,
        settings: true,
        updatedAt: true,
      },
    });

    // Track settings change
    await prisma.analytics.create({
      data: {
        userId: updatedUser.id,
        action: "settings_changed",
        metadata: {
          changedSettings: Object.keys(settings),
          sessionId: request.headers.get("x-session-id") || undefined,
        },
      },
    });

    return NextResponse.json({
      message: "Settings updated successfully",
      settings: updatedUser.settings,
    });
  } catch (error) {
    console.error("Error updating user settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { settings: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ settings: user.settings });
  } catch (error) {
    console.error("Error fetching user settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
