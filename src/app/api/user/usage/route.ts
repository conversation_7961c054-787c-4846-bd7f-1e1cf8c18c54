import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "month";

    // Calculate date range
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case "day":
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case "week":
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "year":
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // month
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    // Get AI usage statistics
    const aiUsage = await prisma.aIUsage.aggregate({
      where: {
        userId: user.id,
        timestamp: {
          gte: startDate,
        },
      },
      _sum: {
        tokens: true,
        cost: true,
      },
      _count: {
        id: true,
      },
    });

    // Get tutorial statistics
    const tutorialStats = await prisma.analytics.groupBy({
      by: ["action"],
      where: {
        userId: user.id,
        timestamp: {
          gte: startDate,
        },
        action: {
          in: ["tutorial_started", "tutorial_completed"],
        },
      },
      _count: {
        id: true,
      },
    });

    // Get voice usage
    const voiceUsage = await prisma.analytics.count({
      where: {
        userId: user.id,
        action: "voice_used",
        timestamp: {
          gte: startDate,
        },
      },
    });

    const tutorialStarted =
      tutorialStats.find((s: any) => s.action === "tutorial_started")?._count.id ||
      0;
    const tutorialCompleted =
      tutorialStats.find((s: any) => s.action === "tutorial_completed")?._count.id ||
      0;

    const usage = {
      period,
      aiRequests: aiUsage._count.id || 0,
      totalTokens: aiUsage._sum.tokens || 0,
      totalCost: aiUsage._sum.cost || 0,
      tutorialViews: tutorialStarted,
      tutorialCompletions: tutorialCompleted,
      completionRate:
        tutorialStarted > 0 ? (tutorialCompleted / tutorialStarted) * 100 : 0,
      voiceRequests: voiceUsage,
      startDate,
      endDate: now,
    };

    return NextResponse.json({ usage });
  } catch (error) {
    console.error("Error fetching user usage:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
