import React, { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HighlightingSystemProps } from "./types";
import { useHighlighting } from "./useHighlighting";
import TooltipComponent from "./TooltipComponent";
import { getOptimalZIndex, getElementBounds } from "./utils";
import { cn } from "@/lib/utils";

const HighlightingSystem: React.FC<HighlightingSystemProps> = ({
  steps,
  config = {},
  onStepChange,
  onComplete,
  onSkip,
  onClose,
  isActive = false,
  initialStep = 0,
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const highlightRef = useRef<HTMLDivElement>(null);

  const {
    currentStep,
    currentStepData,
    highlightedElement,
    tooltipPosition,
    navigationState,
    isActive: tutorialActive,
    nextStep,
    previousStep,
    skipTutorial,
    closeTutorial,
    startTutorial,
  } = useHighlighting({
    steps,
    config: {
      theme: "auto",
      animationDuration: 300,
      showProgress: true,
      allowSkip: true,
      keyboardNavigation: true,
      autoAdvance: false,
      autoAdvanceDelay: 5000,
      zIndexBase: 10000,
      ...config,
    },
    onStepChange,
    onComplete,
  });

  const zIndices = getOptimalZIndex(config.zIndexBase);
  const isDark =
    config.theme === "dark" ||
    (config.theme === "auto" &&
      typeof window !== "undefined" &&
      window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches);

  // Start tutorial when isActive prop changes
  useEffect(() => {
    if (isActive && !tutorialActive) {
      startTutorial();
    } else if (!isActive && tutorialActive) {
      closeTutorial();
    }
  }, [isActive, tutorialActive, startTutorial, closeTutorial]);

  // Handle custom callbacks
  const handleNext = () => {
    nextStep();
  };

  const handlePrevious = () => {
    previousStep();
  };

  const handleSkip = () => {
    onSkip?.();
    skipTutorial();
  };

  const handleClose = () => {
    onClose?.();
    closeTutorial();
  };

  // Get highlight styles
  const getHighlightStyles = () => {
    if (!highlightedElement || !currentStepData) return {};

    const bounds = getElementBounds(highlightedElement);
    const highlightStyle = currentStepData.highlightStyle || {};

    return {
      position: "fixed" as const,
      top: bounds.top - (highlightStyle.borderWidth || 2),
      left: bounds.left - (highlightStyle.borderWidth || 2),
      width: bounds.width + (highlightStyle.borderWidth || 2) * 2,
      height: bounds.height + (highlightStyle.borderWidth || 2) * 2,
      borderRadius: highlightStyle.borderRadius || 4,
      zIndex: zIndices.highlight,
      pointerEvents: "none" as const,
    };
  };

  const getOverlayMaskPath = () => {
    if (!highlightedElement) return "";

    try {
      const bounds = getElementBounds(highlightedElement);
      const padding = 4;

      // Create SVG path for overlay with cutout
      const windowHeight =
        typeof window !== "undefined" ? window.innerHeight : 800;
      const windowWidth =
        typeof window !== "undefined" ? window.innerWidth : 1200;
      return `M0,0 L0,${windowHeight} L${windowWidth},${windowHeight} L${windowWidth},0 Z M${bounds.left - padding},${bounds.top - padding} L${bounds.right + padding},${bounds.top - padding} L${bounds.right + padding},${bounds.bottom + padding} L${bounds.left - padding},${bounds.bottom + padding} Z`;
    } catch (error) {
      return "";
    }
  };

  if (!tutorialActive || !currentStepData) {
    return null;
  }

  return (
    <AnimatePresence>
      {tutorialActive && (
        <>
          {/* Overlay */}
          <motion.div
            ref={overlayRef}
            className="fixed inset-0 pointer-events-none"
            style={{ zIndex: zIndices.overlay }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: (config.animationDuration || 300) / 1000 }}
          >
            <svg
              className="absolute inset-0 w-full h-full"
              style={{ pointerEvents: "none" }}
            >
              <defs>
                <mask id="highlight-mask">
                  <rect width="100%" height="100%" fill="white" />
                  <path
                    d={getOverlayMaskPath()}
                    fill="black"
                    fillRule="evenodd"
                  />
                </mask>
              </defs>
              <rect
                width="100%"
                height="100%"
                fill={isDark ? "rgba(0, 0, 0, 0.7)" : "rgba(0, 0, 0, 0.5)"}
                mask="url(#highlight-mask)"
              />
            </svg>
          </motion.div>

          {/* Highlight border */}
          {highlightedElement && (
            <motion.div
              ref={highlightRef}
              className={cn(
                "pointer-events-none border-2 border-solid",
                currentStepData.highlightStyle?.pulseAnimation !== false &&
                  "animate-pulse",
              )}
              style={{
                ...getHighlightStyles(),
                borderColor:
                  currentStepData.highlightStyle?.borderColor || "#3b82f6",
                backgroundColor:
                  currentStepData.highlightStyle?.backgroundColor ||
                  "transparent",
                boxShadow: `0 0 ${currentStepData.highlightStyle?.shadowBlur || 20}px ${currentStepData.highlightStyle?.shadowColor || "rgba(59, 130, 246, 0.5)"}`,
              }}
              initial={{
                opacity: 0,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                scale: 1,
              }}
              exit={{
                opacity: 0,
                scale: 0.95,
              }}
              transition={{
                duration: (config.animationDuration || 300) / 1000,
                ease: "easeOut",
              }}
            />
          )}

          {/* Tooltip */}
          <TooltipComponent
            step={currentStepData}
            position={
              tooltipPosition || {
                x: 0,
                y: 0,
                placement: "top",
                arrow: { x: 0, y: 0, side: "top" },
              }
            }
            isVisible={!!tooltipPosition}
            theme={config.theme || "auto"}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onSkip={handleSkip}
            onClose={handleClose}
            navigationState={navigationState}
            animationDuration={config.animationDuration || 300}
          />
        </>
      )}
    </AnimatePresence>
  );
};

export default HighlightingSystem;
