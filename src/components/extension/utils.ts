import {
  ElementBounds,
  ViewportBounds,
  TooltipBounds,
  CalculatedPosition,
  TooltipPosition,
} from "./types";

/**
 * Get element bounds relative to viewport
 */
export function getElementBounds(element: Element): ElementBounds {
  const rect = element.getBoundingClientRect();
  return {
    top: rect.top,
    left: rect.left,
    width: rect.width,
    height: rect.height,
    right: rect.right,
    bottom: rect.bottom,
  };
}

/**
 * Get viewport bounds
 */
export function getViewportBounds(): ViewportBounds {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    scrollX: window.scrollX,
    scrollY: window.scrollY,
  };
}

/**
 * Find element by selector with fallback strategies
 */
export function findElement(selector: string): Element | null {
  // Check if we're in a browser environment
  if (typeof document === "undefined") {
    return null;
  }

  try {
    // Try CSS selector first
    let element = document.querySelector(selector);
    if (element) return element;

    // Try XPath if selector starts with '/'
    if (selector.startsWith("/")) {
      const result = document.evaluate(
        selector,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null,
      );
      element = result.singleNodeValue as Element;
      if (element) return element;
    }

    // Try data attribute fallback
    element = document.querySelector(`[data-tutorial="${selector}"]`);
    if (element) return element;

    // Try ID fallback
    element = document.getElementById(selector);
    if (element) return element;

    return null;
  } catch (error) {
    console.warn(`Failed to find element with selector: ${selector}`, error);
    return null;
  }
}

/**
 * Calculate optimal tooltip position with collision detection
 */
export function calculateTooltipPosition(
  elementBounds: ElementBounds,
  tooltipBounds: TooltipBounds,
  preferredPosition: TooltipPosition,
  viewport: ViewportBounds,
): CalculatedPosition {
  const margin = 12; // Minimum margin from viewport edges
  const arrowSize = 8;

  let placement = preferredPosition.placement;
  let x = 0;
  let y = 0;

  // Calculate positions for each placement
  const positions = {
    top: {
      x: elementBounds.left + elementBounds.width / 2 - tooltipBounds.width / 2,
      y: elementBounds.top - tooltipBounds.height - arrowSize,
    },
    bottom: {
      x: elementBounds.left + elementBounds.width / 2 - tooltipBounds.width / 2,
      y: elementBounds.bottom + arrowSize,
    },
    left: {
      x: elementBounds.left - tooltipBounds.width - arrowSize,
      y:
        elementBounds.top + elementBounds.height / 2 - tooltipBounds.height / 2,
    },
    right: {
      x: elementBounds.right + arrowSize,
      y:
        elementBounds.top + elementBounds.height / 2 - tooltipBounds.height / 2,
    },
  };

  // Check if preferred position fits
  if (placement === "auto") {
    // Find best position automatically
    const priorities = ["bottom", "top", "right", "left"] as const;
    for (const pos of priorities) {
      const candidate = positions[pos];
      if (isPositionValid(candidate, tooltipBounds, viewport, margin)) {
        placement = pos;
        break;
      }
    }
  }

  // Get position for chosen placement
  const chosenPosition = positions[placement as keyof typeof positions];
  x = chosenPosition.x;
  y = chosenPosition.y;

  // Apply offset if provided
  if (preferredPosition.offset) {
    x += preferredPosition.offset.x;
    y += preferredPosition.offset.y;
  }

  // Ensure tooltip stays within viewport bounds
  x = Math.max(
    margin,
    Math.min(x, viewport.width - tooltipBounds.width - margin),
  );
  y = Math.max(
    margin,
    Math.min(y, viewport.height - tooltipBounds.height - margin),
  );

  // Calculate arrow position
  const arrow = calculateArrowPosition(
    { x, y },
    elementBounds,
    tooltipBounds,
    placement as TooltipPosition["placement"],
  );

  return {
    x,
    y,
    placement: placement as TooltipPosition["placement"],
    arrow,
  };
}

/**
 * Check if position is valid within viewport
 */
function isPositionValid(
  position: { x: number; y: number },
  tooltipBounds: TooltipBounds,
  viewport: ViewportBounds,
  margin: number,
): boolean {
  return (
    position.x >= margin &&
    position.y >= margin &&
    position.x + tooltipBounds.width <= viewport.width - margin &&
    position.y + tooltipBounds.height <= viewport.height - margin
  );
}

/**
 * Calculate arrow position relative to tooltip
 */
function calculateArrowPosition(
  tooltipPosition: { x: number; y: number },
  elementBounds: ElementBounds,
  tooltipBounds: TooltipBounds,
  placement: TooltipPosition["placement"],
): CalculatedPosition["arrow"] {
  const elementCenterX = elementBounds.left + elementBounds.width / 2;
  const elementCenterY = elementBounds.top + elementBounds.height / 2;

  let arrowX = 0;
  let arrowY = 0;
  let side: CalculatedPosition["arrow"]["side"];

  switch (placement) {
    case "top":
      arrowX = elementCenterX - tooltipPosition.x;
      arrowY = tooltipBounds.height;
      side = "bottom";
      break;
    case "bottom":
      arrowX = elementCenterX - tooltipPosition.x;
      arrowY = 0;
      side = "top";
      break;
    case "left":
      arrowX = tooltipBounds.width;
      arrowY = elementCenterY - tooltipPosition.y;
      side = "right";
      break;
    case "right":
      arrowX = 0;
      arrowY = elementCenterY - tooltipPosition.y;
      side = "left";
      break;
    default:
      arrowX = tooltipBounds.width / 2;
      arrowY = tooltipBounds.height;
      side = "bottom";
  }

  // Clamp arrow position within tooltip bounds
  const arrowMargin = 16;
  if (placement === "top" || placement === "bottom") {
    arrowX = Math.max(
      arrowMargin,
      Math.min(arrowX, tooltipBounds.width - arrowMargin),
    );
  } else {
    arrowY = Math.max(
      arrowMargin,
      Math.min(arrowY, tooltipBounds.height - arrowMargin),
    );
  }

  return { x: arrowX, y: arrowY, side };
}

/**
 * Get optimal z-index for overlay elements
 */
export function getOptimalZIndex(baseZIndex: number = 10000): {
  overlay: number;
  highlight: number;
  tooltip: number;
} {
  // Check if we're in a browser environment
  if (typeof document === "undefined" || typeof window === "undefined") {
    return {
      overlay: baseZIndex + 1000,
      highlight: baseZIndex + 1001,
      tooltip: baseZIndex + 1002,
    };
  }

  try {
    // Find highest z-index on page
    const allElements = document.querySelectorAll("*");
    let maxZIndex = baseZIndex;

    for (const element of Array.from(allElements)) {
      const zIndex = parseInt(
        window.getComputedStyle(element).zIndex || "0",
        10,
      );
      if (!isNaN(zIndex) && zIndex > maxZIndex) {
        maxZIndex = zIndex;
      }
    }

    return {
      overlay: maxZIndex + 1000,
      highlight: maxZIndex + 1001,
      tooltip: maxZIndex + 1002,
    };
  } catch (error) {
    // Fallback if DOM access fails
    return {
      overlay: baseZIndex + 1000,
      highlight: baseZIndex + 1001,
      tooltip: baseZIndex + 1002,
    };
  }
}

/**
 * Scroll element into view smoothly
 */
export function scrollElementIntoView(
  element: Element,
  options: {
    behavior?: ScrollBehavior;
    block?: ScrollLogicalPosition;
    inline?: ScrollLogicalPosition;
    offset?: { x: number; y: number };
  } = {},
): Promise<void> {
  return new Promise((resolve) => {
    const {
      behavior = "smooth",
      block = "center",
      inline = "center",
      offset = { x: 0, y: 0 },
    } = options;

    // Scroll element into view
    element.scrollIntoView({ behavior, block, inline });

    // Apply additional offset if needed
    if (offset.x !== 0 || offset.y !== 0) {
      setTimeout(
        () => {
          window.scrollBy({
            left: offset.x,
            top: offset.y,
            behavior,
          });
          resolve();
        },
        behavior === "smooth" ? 300 : 0,
      );
    } else {
      resolve();
    }
  });
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number,
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Check if element is visible in viewport
 */
export function isElementVisible(element: Element): boolean {
  // Check if we're in a browser environment
  if (typeof window === "undefined") {
    return false;
  }

  try {
    const rect = element.getBoundingClientRect();
    const viewport = getViewportBounds();

    return (
      rect.top < viewport.height &&
      rect.bottom > 0 &&
      rect.left < viewport.width &&
      rect.right > 0
    );
  } catch (error) {
    return false;
  }
}

/**
 * Generate unique ID for elements
 */
export function generateId(prefix: string = "highlight"): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get computed style property value
 */
export function getComputedStyleValue(
  element: Element,
  property: string,
): string {
  // Check if we're in a browser environment
  if (typeof window === "undefined") {
    return "";
  }

  try {
    return window.getComputedStyle(element).getPropertyValue(property);
  } catch (error) {
    return "";
  }
}

/**
 * Check if device supports touch
 */
export function isTouchDevice(): boolean {
  // Check if we're in a browser environment
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  try {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Get element's text content for accessibility
 */
export function getElementDescription(element: Element): string {
  // Try aria-label first
  const ariaLabel = element.getAttribute("aria-label");
  if (ariaLabel) return ariaLabel;

  // Try title attribute
  const title = element.getAttribute("title");
  if (title) return title;

  // Try alt attribute for images
  const alt = element.getAttribute("alt");
  if (alt) return alt;

  // Try text content (truncated)
  const textContent = element.textContent?.trim();
  if (textContent && textContent.length > 0) {
    return textContent.length > 100
      ? textContent.substring(0, 100) + "..."
      : textContent;
  }

  // Fallback to tag name
  return element.tagName.toLowerCase();
}
