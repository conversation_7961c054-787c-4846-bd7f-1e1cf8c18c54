import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { NavigationControlsProps } from "./types";
import { cn } from "@/lib/utils";
import {
  ChevronLeft,
  ChevronRight,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Play,
  Pause,
} from "lucide-react";

const NavigationControls: React.FC<NavigationControlsProps> = ({
  navigationState,
  onNext,
  onPrevious,
  onSkip,
  onClose,
  allowSkip = true,
  theme = "auto",
}) => {
  const { canGoNext, canGoPrevious, isPlaying } = navigationState;
  const isDark =
    theme === "dark" ||
    (theme === "auto" &&
      window.matchMedia("(prefers-color-scheme: dark)").matches);

  const buttonVariant = isDark ? "secondary" : "outline";
  const primaryButtonVariant = "default";

  return (
    <motion.div
      className="flex items-center justify-between space-x-2"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.2 }}
    >
      {/* Left side controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant={buttonVariant}
          size="sm"
          onClick={onPrevious}
          disabled={!canGoPrevious}
          className={cn(
            "h-8 px-3 transition-all duration-200",
            !canGoPrevious && "opacity-50 cursor-not-allowed",
          )}
          aria-label="Previous step"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>

        {allowSkip && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onSkip}
            className={cn(
              "h-8 px-3 text-xs transition-all duration-200",
              isDark
                ? "text-white/60 hover:text-white/80"
                : "text-black/60 hover:text-black/80",
            )}
            aria-label="Skip tutorial"
          >
            <SkipForward className="h-3 w-3 mr-1" />
            Skip
          </Button>
        )}
      </div>

      {/* Center play/pause indicator */}
      <div className="flex items-center">
        <motion.div
          className={cn(
            "flex items-center justify-center w-6 h-6 rounded-full",
            isDark ? "bg-white/10" : "bg-black/10",
          )}
          animate={{
            scale: isPlaying ? [1, 1.1, 1] : 1,
          }}
          transition={{
            duration: 2,
            repeat: isPlaying ? Infinity : 0,
            ease: "easeInOut",
          }}
        >
          {isPlaying ? (
            <Play
              className={cn(
                "h-3 w-3",
                isDark ? "text-white/60" : "text-black/60",
              )}
            />
          ) : (
            <Pause
              className={cn(
                "h-3 w-3",
                isDark ? "text-white/60" : "text-black/60",
              )}
            />
          )}
        </motion.div>
      </div>

      {/* Right side controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant={canGoNext ? primaryButtonVariant : buttonVariant}
          size="sm"
          onClick={onNext}
          disabled={!canGoNext}
          className={cn(
            "h-8 px-3 transition-all duration-200",
            !canGoNext && "opacity-50 cursor-not-allowed",
          )}
          aria-label={canGoNext ? "Next step" : "Complete tutorial"}
        >
          {canGoNext ? (
            <>
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </>
          ) : (
            "Complete"
          )}
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className={cn(
            "h-8 w-8 p-0 transition-all duration-200",
            isDark
              ? "text-white/60 hover:text-white/80"
              : "text-black/60 hover:text-black/80",
          )}
          aria-label="Close tutorial"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </motion.div>
  );
};

export default NavigationControls;
