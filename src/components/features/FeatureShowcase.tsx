"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { Button } from "../ui/button";
import { ChevronDown, ChevronUp, Mic, Brain, Building } from "lucide-react";

interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  details: string;
}

const FeatureCard = ({
  title,
  description,
  icon,
  details,
}: FeatureCardProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="bg-white dark:bg-gray-900 overflow-hidden transition-all duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              {icon}
            </div>
            <CardTitle>{title}</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsExpanded(!isExpanded)}
            aria-label={isExpanded ? "Collapse details" : "Expand details"}
          >
            {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </Button>
        </div>
        <CardDescription className="pt-2">{description}</CardDescription>
      </CardHeader>
      <motion.div
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isExpanded ? "auto" : 0,
          opacity: isExpanded ? 1 : 0,
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <CardContent className="pt-0">
          <p className="text-sm text-muted-foreground">{details}</p>
        </CardContent>
      </motion.div>
    </Card>
  );
};

const FeatureShowcase = () => {
  const features = [
    {
      title: "AI Integration",
      description: "Powerful AI-driven explanations and tutorials",
      icon: <Brain size={24} />,
      details:
        "Leverage multiple AI providers including OpenAI, Anthropic, and Google AI to deliver context-aware explanations for any webpage element. Our system analyzes page content in real-time to provide the most relevant and helpful guidance.",
    },
    {
      title: "Voice Capabilities",
      description: "Natural voice interaction and premium text-to-speech",
      icon: <Mic size={24} />,
      details:
        "Experience premium text-to-speech via ElevenLabs with customizable voice settings. Use voice commands for hands-free navigation through tutorials, and enjoy multi-language voice support for global accessibility.",
    },
    {
      title: "Enterprise Features",
      description: "Advanced tools for organizational deployment",
      icon: <Building size={24} />,
      details:
        "Deploy at scale with white-label customization, SSO integration with major providers, comprehensive analytics and reporting, and priority support with dedicated SLAs. Custom domain support and API access enable seamless integration with existing systems.",
    },
  ];

  return (
    <section className="w-full py-12 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold tracking-tight mb-2">
            Powerful Features
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Our AI-powered browser extension combines cutting-edge technology
            with intuitive design to deliver an unparalleled tutoring
            experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <FeatureCard
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                details={feature.details}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeatureShowcase;
