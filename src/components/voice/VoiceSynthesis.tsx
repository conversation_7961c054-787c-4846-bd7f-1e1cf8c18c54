"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Play,
  Pause,
  Square,
  Volume2,
  VolumeX,
  Settings,
  Loader2,
  Mic,
  MicOff,
} from "lucide-react";

interface Voice {
  id: string;
  name: string;
  language: string;
  gender: string;
  provider: string;
  available: boolean;
}

interface VoiceSynthesisProps {
  text: string;
  autoPlay?: boolean;
  showControls?: boolean;
  language?: string;
  quality?: "low" | "medium" | "high" | "premium";
  enableCache?: boolean;
  onPlayStart?: () => void;
  onPlayEnd?: () => void;
  onError?: (error: string) => void;
  onUsageUpdate?: (usage: any) => void;
  enableVoiceCommands?: boolean;
  onVoiceCommand?: (command: any) => void;
}

const VoiceSynthesis: React.FC<VoiceSynthesisProps> = ({
  text,
  autoPlay = false,
  showControls = true,
  language,
  quality = "medium",
  enableCache = true,
  onPlayStart,
  onPlayEnd,
  onError,
  onUsageUpdate,
  enableVoiceCommands = false,
  onVoiceCommand,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>("rachel");
  const [speed, setSpeed] = useState([1.0]);
  const [volume, setVolume] = useState([0.8]);
  const [isMuted, setIsMuted] = useState(false);
  const [useBrowserTTS, setUseBrowserTTS] = useState(false);
  const [currentQuality, setCurrentQuality] = useState(quality);
  const [detectedLanguage, setDetectedLanguage] = useState(language || "en");
  const [usage, setUsage] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [errorDetails, setErrorDetails] = useState<string>("");
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null,
  );
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([]);

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const speechSynthesisRef = useRef<SpeechSynthesisUtterance | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Load available voices and configuration
  useEffect(() => {
    const loadVoices = async () => {
      try {
        const response = await fetch("/api/voice/synthesize");
        if (response.ok) {
          const data = await response.json();
          setVoices(data.voices || []);
          setUsage(data.usage);
          setCacheStats(data.cache);
          onUsageUpdate?.(data.usage);

          // Auto-detect language if not provided
          if (!language && text) {
            const detected = detectTextLanguage(text);
            setDetectedLanguage(detected);

            // Select appropriate voice for detected language
            const languageVoices = data.voices.filter(
              (v: Voice) =>
                v.language === detected && v.provider === "elevenlabs",
            );
            if (languageVoices.length > 0) {
              setSelectedVoice(languageVoices[0].id);
            }
          }
        }
      } catch (error) {
        console.error("Failed to load voices:", error);
        setErrorDetails("Failed to load voice configuration");
        loadBrowserVoices();
      }
    };

    loadVoices();
  }, [language, text, onUsageUpdate]);

  // Simple language detection
  const detectTextLanguage = (text: string): string => {
    if (/[\u4e00-\u9fff]/.test(text)) return "zh";
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return "ja";
    if (/[\uac00-\ud7af]/.test(text)) return "ko";
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/.test(text)) {
      if (/[àáâãäåæçèéêëìíîïñòóôõöøùúûüý]/.test(text)) return "fr";
      if (/[äöüß]/.test(text)) return "de";
      if (/[àèéìíîòóù]/.test(text)) return "it";
      if (/[ãçõ]/.test(text)) return "pt";
      if (/[ñáéíóúü]/.test(text)) return "es";
    }
    return "en";
  };

  // Load browser voices as fallback
  const loadBrowserVoices = () => {
    if (typeof window !== "undefined" && "speechSynthesis" in window) {
      const browserVoices = speechSynthesis.getVoices();
      const formattedVoices: Voice[] = browserVoices.map((voice) => ({
        id: voice.name,
        name: voice.name,
        language: voice.lang,
        gender: "neutral",
        provider: "browser",
        available: true,
      }));
      setVoices((prev) => [...prev, ...formattedVoices]);
    }
  };

  const startVoiceRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "inactive") {
      setIsRecording(true);
      setRecordedChunks([]);
      mediaRecorder.start();
    }
  };

  const stopVoiceRecording = () => {
    if (mediaRecorder && mediaRecorder.state === "recording") {
      setIsRecording(false);
      mediaRecorder.stop();
    }
  };

  const processVoiceCommand = async (audioBlob: Blob) => {
    try {
      const formData = new FormData();
      formData.append("audio", audioBlob, "command.webm");
      formData.append(
        "context",
        JSON.stringify({
          pageUrl: window.location.href,
          timestamp: new Date().toISOString(),
        }),
      );

      const response = await fetch("/api/voice/command", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        onVoiceCommand?.(result);
      } else {
        console.error("Voice command processing failed:", response.status);
      }
    } catch (error) {
      console.error("Voice command error:", error);
      onError?.("Voice command processing failed");
    }
  };

  // Auto-play functionality
  useEffect(() => {
    if (autoPlay && text && !isPlaying) {
      handlePlay();
    }
  }, [autoPlay, text]);

  // Initialize voice command recording
  useEffect(() => {
    if (
      enableVoiceCommands &&
      typeof window !== "undefined" &&
      navigator.mediaDevices
    ) {
      navigator.mediaDevices
        .getUserMedia({ audio: true })
        .then((stream) => {
          const recorder = new MediaRecorder(stream);
          setMediaRecorder(recorder);

          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              setRecordedChunks((prev) => [...prev, event.data]);
            }
          };

          recorder.onstop = async () => {
            if (recordedChunks.length > 0) {
              const audioBlob = new Blob(recordedChunks, {
                type: "audio/webm",
              });
              await processVoiceCommand(audioBlob);
              setRecordedChunks([]);
            }
          };
        })
        .catch((error) => {
          console.error("Failed to initialize voice recording:", error);
        });
    }
  }, [enableVoiceCommands, recordedChunks]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      handleStop();
      if (mediaRecorder) {
        mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      }
    };
  }, [mediaRecorder]);

  const handlePlay = async () => {
    if (!text.trim()) {
      onError?.("No text to synthesize");
      return;
    }

    setIsLoading(true);
    onPlayStart?.();

    try {
      if (selectedVoice !== "default" && !useBrowserTTS) {
        await playWithElevenLabs();
      } else {
        await playWithBrowserTTS();
      }
    } catch (error) {
      console.error("Voice synthesis error:", error);
      onError?.(
        error instanceof Error ? error.message : "Voice synthesis failed",
      );
      if (!useBrowserTTS) {
        setUseBrowserTTS(true);
        await playWithBrowserTTS();
      }
    } finally {
      setIsLoading(false);
    }
  };

  const playWithElevenLabs = async () => {
    abortControllerRef.current = new AbortController();

    const requestBody = {
      text,
      voice: selectedVoice,
      speed: speed[0],
      language: detectedLanguage,
      quality: currentQuality,
      enableCache,
    };

    const response = await fetch("/api/voice/synthesize", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      signal: abortControllerRef.current.signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const contentType = response.headers.get("content-type");

    if (contentType?.includes("audio")) {
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      audioRef.current = new Audio(audioUrl);
      audioRef.current.volume = isMuted ? 0 : volume[0];
      audioRef.current.playbackRate = speed[0];

      audioRef.current.onplay = () => setIsPlaying(true);
      audioRef.current.onended = () => {
        setIsPlaying(false);
        onPlayEnd?.();
        URL.revokeObjectURL(audioUrl);
      };
      audioRef.current.onerror = () => {
        setIsPlaying(false);
        onError?.("Audio playback failed");
        URL.revokeObjectURL(audioUrl);
      };

      await audioRef.current.play();
    } else {
      const data = await response.json();
      if (data.useBrowserTTS) {
        setUseBrowserTTS(true);
        await playWithBrowserTTS();
      }
    }
  };

  const playWithBrowserTTS = async () => {
    if (typeof window === "undefined" || (!"speechSynthesis") in window) {
      throw new Error("Speech synthesis not supported");
    }

    speechSynthesis.cancel();

    speechSynthesisRef.current = new SpeechSynthesisUtterance(text);

    const browserVoices = speechSynthesis.getVoices();
    const selectedBrowserVoice = browserVoices.find(
      (voice) => voice.name === selectedVoice,
    );

    if (selectedBrowserVoice) {
      speechSynthesisRef.current.voice = selectedBrowserVoice;
    }

    speechSynthesisRef.current.rate = speed[0];
    speechSynthesisRef.current.volume = isMuted ? 0 : volume[0];
    speechSynthesisRef.current.pitch = 1;

    speechSynthesisRef.current.onstart = () => setIsPlaying(true);
    speechSynthesisRef.current.onend = () => {
      setIsPlaying(false);
      onPlayEnd?.();
    };
    speechSynthesisRef.current.onerror = (event) => {
      setIsPlaying(false);
      onError?.(event.error || "Speech synthesis failed");
    };

    speechSynthesis.speak(speechSynthesisRef.current);
  };

  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      audioRef.current = null;
    }

    if (speechSynthesis.speaking || speechSynthesis.pending) {
      speechSynthesis.cancel();
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    setIsPlaying(false);
    setIsLoading(false);
  };

  const toggleMute = () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);

    if (audioRef.current) {
      audioRef.current.volume = newMuted ? 0 : volume[0];
    }

    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.volume = newMuted ? 0 : volume[0];
    }
  };

  if (!showControls) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={isPlaying ? () => handleStop() : handlePlay}
        disabled={isLoading || !text.trim()}
        className="h-8 w-8 p-0"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : isPlaying ? (
          <Pause className="h-4 w-4" />
        ) : (
          <Play className="h-4 w-4" />
        )}
      </Button>
    );
  }

  return (
    <Card className="w-full max-w-md bg-background">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Voice Synthesis</CardTitle>
          <div className="flex items-center space-x-1">
            {useBrowserTTS && (
              <Badge variant="outline" className="text-xs">
                Browser TTS
              </Badge>
            )}
            <Badge variant="outline" className="text-xs">
              {voices.find((v) => v.id === selectedVoice)?.provider ||
                "Browser"}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-xs font-medium">Voice</label>
          <Select value={selectedVoice} onValueChange={setSelectedVoice}>
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {voices.map((voice) => (
                <SelectItem
                  key={voice.id}
                  value={voice.id}
                  disabled={!voice.available}
                >
                  <div className="flex items-center justify-between w-full">
                    <span>{voice.name}</span>
                    <Badge variant="outline" className="ml-2 text-xs">
                      {voice.provider}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={isPlaying ? () => handleStop() : handlePlay}
                    disabled={isLoading || !text.trim()}
                    className="h-8 w-8 p-0"
                  >
                    {isLoading ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : isPlaying ? (
                      <Pause className="h-3 w-3" />
                    ) : (
                      <Play className="h-3 w-3" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{isPlaying ? "Stop" : "Play"}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className="h-8 w-8 p-0"
            >
              {isMuted ? (
                <VolumeX className="h-3 w-3" />
              ) : (
                <Volume2 className="h-3 w-3" />
              )}
            </Button>

            {enableVoiceCommands && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isRecording ? "destructive" : "outline"}
                      size="sm"
                      onClick={
                        isRecording ? stopVoiceRecording : startVoiceRecording
                      }
                      disabled={!mediaRecorder}
                      className="h-8 w-8 p-0"
                    >
                      {isRecording ? (
                        <MicOff className="h-3 w-3" />
                      ) : (
                        <Mic className="h-3 w-3" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isRecording ? "Stop Recording" : "Voice Command"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium">Volume</label>
            <span className="text-xs text-muted-foreground">
              {Math.round(volume[0] * 100)}%
            </span>
          </div>
          <Slider
            value={volume}
            onValueChange={setVolume}
            max={1}
            min={0}
            step={0.1}
            className="w-full"
            disabled={isMuted}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium">Speed</label>
            <span className="text-xs text-muted-foreground">
              {speed[0].toFixed(1)}x
            </span>
          </div>
          <Slider
            value={speed}
            onValueChange={setSpeed}
            max={2}
            min={0.5}
            step={0.1}
            className="w-full"
          />
        </div>

        {text && (
          <div className="space-y-2">
            <label className="text-xs font-medium">Text</label>
            <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded max-h-20 overflow-y-auto">
              {text.length > 100 ? `${text.substring(0, 100)}...` : text}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VoiceSynthesis;
