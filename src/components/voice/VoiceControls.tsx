"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from "lucide-react";
import VoiceSynthesis from "./VoiceSynthesis";

interface VoiceControlsProps {
  text?: string;
  onVoiceCommand?: (command: string) => void;
  onSpeechStart?: () => void;
  onSpeechEnd?: () => void;
  showSynthesis?: boolean;
  showRecognition?: boolean;
  className?: string;
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  text = "",
  onVoiceCommand,
  onSpeechStart,
  onSpeechEnd,
  showSynthesis = true,
  showRecognition = true,
  className = "",
}) => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(true);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(
    null,
  );
  const [isSupported, setIsSupported] = useState(false);
  const [lastCommand, setLastCommand] = useState<string>("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;

      if (SpeechRecognition) {
        const recognitionInstance = new SpeechRecognition();
        recognitionInstance.continuous = false;
        recognitionInstance.interimResults = false;
        recognitionInstance.lang = "en-US";

        recognitionInstance.onstart = () => {
          setIsListening(true);
        };

        recognitionInstance.onresult = (event) => {
          const command = event.results[0][0].transcript.toLowerCase().trim();
          setLastCommand(command);
          onVoiceCommand?.(command);
        };

        recognitionInstance.onend = () => {
          setIsListening(false);
        };

        recognitionInstance.onerror = (event) => {
          console.error("Speech recognition error:", event.error);
          setIsListening(false);
        };

        setRecognition(recognitionInstance);
        setIsSupported(true);
      } else {
        console.warn("Speech recognition not supported in this browser");
        setIsSupported(false);
      }
    }
  }, [onVoiceCommand]);

  const toggleListening = () => {
    if (recognition) {
      if (isListening) {
        recognition.stop();
      } else {
        try {
          recognition.start();
        } catch (error) {
          console.error("Failed to start speech recognition:", error);
        }
      }
    }
  };

  const toggleVoice = () => {
    setIsVoiceEnabled(!isVoiceEnabled);
    if (isSpeaking) {
      if (typeof window !== "undefined" && "speechSynthesis" in window) {
        speechSynthesis.cancel();
      }
      setIsSpeaking(false);
    }
  };

  const handleSpeechStart = () => {
    setIsSpeaking(true);
    onSpeechStart?.();
  };

  const handleSpeechEnd = () => {
    setIsSpeaking(false);
    onSpeechEnd?.();
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showRecognition && isSupported && (
        <div className="flex items-center space-x-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={isListening ? "default" : "outline"}
                  size="sm"
                  onClick={toggleListening}
                  className={`h-8 w-8 p-0 ${
                    isListening ? "animate-pulse" : ""
                  }`}
                >
                  {isListening ? (
                    <Mic className="h-4 w-4" />
                  ) : (
                    <MicOff className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {isListening ? "Stop listening" : "Start voice recognition"}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {lastCommand && (
            <Badge variant="outline" className="text-xs max-w-32 truncate">
              {lastCommand}
            </Badge>
          )}
        </div>
      )}

      {showSynthesis && text && (
        <div className="flex items-center space-x-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleVoice}
                  className="h-8 w-8 p-0"
                >
                  {isVoiceEnabled ? (
                    <Volume2 className="h-4 w-4" />
                  ) : (
                    <VolumeX className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isVoiceEnabled ? "Disable voice" : "Enable voice"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {isVoiceEnabled && (
            <VoiceSynthesis
              text={text}
              showControls={false}
              onPlayStart={handleSpeechStart}
              onPlayEnd={handleSpeechEnd}
            />
          )}
        </div>
      )}

      <div className="flex items-center space-x-1">
        {isListening && (
          <Badge variant="default" className="text-xs animate-pulse">
            Listening...
          </Badge>
        )}

        {isSpeaking && (
          <Badge variant="secondary" className="text-xs">
            Speaking...
          </Badge>
        )}

        {!isSupported && showRecognition && (
          <Badge variant="destructive" className="text-xs">
            Voice not supported
          </Badge>
        )}
      </div>
    </div>
  );
};

export default VoiceControls;
