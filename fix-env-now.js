#!/usr/bin/env node

/**
 * Quick fix for environment validation issues
 * This script will fix the immediate NEXTAUTH_SECRET issue
 */

const fs = require('fs');
const crypto = require('crypto');

console.log('🔧 Fixing environment validation issues...\n');

// Read current .env file
let envContent = '';
try {
  envContent = fs.readFileSync('.env', 'utf8');
} catch (error) {
  console.error('❌ .env file not found');
  process.exit(1);
}

// Check current NEXTAUTH_SECRET
const lines = envContent.split('\n');
const nextAuthLineIndex = lines.findIndex(line => line.startsWith('NEXTAUTH_SECRET='));

if (nextAuthLineIndex === -1) {
  console.log('❌ NEXTAUTH_SECRET not found in .env file');
  process.exit(1);
}

const currentSecret = lines[nextAuthLineIndex].split('=')[1].replace(/"/g, '');
console.log(`Current NEXTAUTH_SECRET length: ${currentSecret.length} characters`);

if (currentSecret.length >= 32) {
  console.log('✅ NEXTAUTH_SECRET is already valid (32+ characters)');
  console.log('✅ Environment should be working now');
  process.exit(0);
}

// Generate new secret
const newSecret = crypto.randomBytes(32).toString('base64');
console.log(`Generated new secret length: ${newSecret.length} characters`);

// Replace the line
lines[nextAuthLineIndex] = `NEXTAUTH_SECRET="${newSecret}"`;

// Write back to file
const newEnvContent = lines.join('\n');
fs.writeFileSync('.env', newEnvContent);

console.log('✅ Updated NEXTAUTH_SECRET in .env file');
console.log('✅ Environment validation should now pass');
console.log('\n🚀 You can now run: npm run dev');

// Verify the fix
const verifyContent = fs.readFileSync('.env', 'utf8');
const verifyLines = verifyContent.split('\n');
const verifyLine = verifyLines.find(line => line.startsWith('NEXTAUTH_SECRET='));
const verifySecret = verifyLine.split('=')[1].replace(/"/g, '');

console.log(`\n🔍 Verification: New secret length is ${verifySecret.length} characters`);
console.log(verifySecret.length >= 32 ? '✅ Valid' : '❌ Still invalid');
