#!/usr/bin/env node

/**
 * Environment Validation Script
 * Run this before starting the application to ensure all required environment variables are set
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  bright: '\x1b[1m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function checkEnvFile() {
  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');

  if (!fs.existsSync(envPath)) {
    logError('.env file not found!');
    if (fs.existsSync(envExamplePath)) {
      logInfo('Copy .env.example to .env and fill in your values:');
      logInfo('cp .env.example .env');
    }
    return false;
  }

  logSuccess('.env file found');
  return true;
}

function validateEnvironment() {
  log('\n🔍 Validating environment configuration...\n', 'bright');

  // Check if .env file exists
  if (!checkEnvFile()) {
    process.exit(1);
  }

  // Load environment variables
  require('dotenv').config();

  const errors = [];
  const warnings = [];

  // Check required variables
  const requiredVars = {
    'NEXTAUTH_URL': {
      value: process.env.NEXTAUTH_URL,
      validator: (val) => val && val.startsWith('http'),
      message: 'Must be a valid URL starting with http/https'
    },
    'NEXTAUTH_SECRET': {
      value: process.env.NEXTAUTH_SECRET,
      validator: (val) => val && val.length >= 32,
      message: 'Must be at least 32 characters long'
    }
  };

  // Database validation
  const hasDbUrl = process.env.DATABASE_URL;
  const hasDbComponents = process.env.DB_HOST && process.env.DB_PASSWORD && process.env.DB_NAME;
  
  if (!hasDbUrl && !hasDbComponents) {
    errors.push('Database configuration incomplete. Either set DATABASE_URL or provide DB_HOST, DB_PASSWORD, and DB_NAME');
  } else {
    logSuccess('Database configuration valid');
  }

  // Validate required variables
  for (const [key, config] of Object.entries(requiredVars)) {
    if (!config.value) {
      errors.push(`${key} is required but not set`);
    } else if (!config.validator(config.value)) {
      errors.push(`${key}: ${config.message}`);
    } else {
      logSuccess(`${key} configuration valid`);
    }
  }

  // Check AI providers
  const aiProviders = [
    process.env.OPENAI_API_KEY,
    process.env.ANTHROPIC_API_KEY,
    process.env.GROQ_API_KEY,
    process.env.OPENROUTER_API_KEY,
    process.env.GOOGLE_AI_API_KEY,
  ].filter(Boolean);

  if (aiProviders.length === 0) {
    if (process.env.NODE_ENV === 'production') {
      errors.push('At least one AI provider API key is required for production');
    } else {
      warnings.push('No AI providers configured (some features may not work)');
    }
  } else {
    logSuccess(`${aiProviders.length} AI provider(s) configured`);
  }

  // Check email configuration
  const emailConfigured = process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASSWORD;
  if (!emailConfigured) {
    if (process.env.NODE_ENV === 'production') {
      warnings.push('Email not configured (password reset will not work)');
    } else {
      logInfo('Email not configured (emails will be logged in development)');
    }
  } else {
    logSuccess('Email configuration valid');
  }

  // Check OAuth
  const googleOAuthConfigured = process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET;
  if (googleOAuthConfigured) {
    logSuccess('Google OAuth configured');
  } else {
    logInfo('Google OAuth not configured (optional)');
  }

  // Environment-specific checks
  if (process.env.NODE_ENV === 'production') {
    if (process.env.NEXTAUTH_URL?.includes('localhost')) {
      errors.push('NEXTAUTH_URL should not use localhost in production');
    }

    if (!process.env.NEXT_PUBLIC_APP_URL) {
      warnings.push('NEXT_PUBLIC_APP_URL not set (recommended for production)');
    }

    logSuccess('Production environment checks completed');
  }

  // Display results
  if (errors.length > 0) {
    log('\n📝 Errors found:', 'red');
    errors.forEach(error => logError(`   ${error}`));
    
    log('\n💡 Tips:', 'yellow');
    log('   - Check your .env file for missing or invalid values');
    log('   - Use .env.example as a reference');
    log('   - Generate secure secrets: openssl rand -base64 32');
    
    process.exit(1);
  }

  if (warnings.length > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    warnings.forEach(warning => logWarning(`   ${warning}`));
  }

  log('\n🎉 Environment validation completed successfully!\n', 'green');

  // Display summary
  log('📋 Configuration Summary:', 'bright');
  log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  log(`   Database: ${hasDbUrl ? 'URL configured' : 'Components configured'}`);
  log(`   AI Providers: ${aiProviders.length} configured`);
  log(`   Email: ${emailConfigured ? 'Configured' : 'Not configured'}`);
  log(`   OAuth: ${googleOAuthConfigured ? 'Google configured' : 'Not configured'}`);
}

function generateSecret() {
  const secret = crypto.randomBytes(32).toString('base64');
  log('\n🔐 Generated NEXTAUTH_SECRET:', 'bright');
  log(`NEXTAUTH_SECRET="${secret}"`);
  log('\n📋 Copy this to your .env file');
}

// Command line interface
const command = process.argv[2];

switch (command) {
  case 'generate-secret':
    generateSecret();
    break;
  case 'validate':
  default:
    validateEnvironment();
    break;
}
