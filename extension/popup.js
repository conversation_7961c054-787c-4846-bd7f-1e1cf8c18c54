/**
 * TutorAI Extension Popup
 * Handles user interactions and communicates with content script
 */

class TutorAIPopup {
  constructor() {
    this.currentTab = null;
    this.isHighlightingEnabled = false;
    this.settings = {};
    
    this.init();
  }

  async init() {
    // Get current tab
    this.currentTab = await this.getCurrentTab();
    
    // Load settings
    await this.loadSettings();
    
    // Update UI
    this.updatePageInfo();
    this.updateStatus();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Check connection status
    this.checkConnectionStatus();
  }

  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['tutorAISettings'], (result) => {
        this.settings = result.tutorAISettings || {
          enabled: true,
          autoStart: false,
          voiceEnabled: false,
          highlightColor: '#3b82f6',
          tutorAIServer: 'http://localhost:3000'
        };
        
        // Update UI with settings
        document.getElementById('auto-start').checked = this.settings.autoStart;
        document.getElementById('voice-enabled').checked = this.settings.voiceEnabled;
        document.getElementById('highlight-color').value = this.settings.highlightColor;
        
        resolve();
      });
    });
  }

  async saveSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.set({ tutorAISettings: this.settings }, resolve);
    });
  }

  updatePageInfo() {
    if (this.currentTab) {
      document.getElementById('page-title').textContent = this.currentTab.title || 'Unknown Page';
      document.getElementById('page-url').textContent = this.currentTab.url || 'Unknown URL';
    }
  }

  updateStatus(status = 'Ready', isConnected = true) {
    const indicator = document.getElementById('status-indicator');
    const statusText = indicator.querySelector('.status-text');
    const statusDot = indicator.querySelector('.status-dot');
    
    statusText.textContent = status;
    statusDot.className = `status-dot ${isConnected ? 'connected' : 'disconnected'}`;
  }

  setupEventListeners() {
    // Quick action buttons
    document.getElementById('start-tutorial').addEventListener('click', () => {
      this.startTutorial();
    });

    document.getElementById('explain-page').addEventListener('click', () => {
      this.explainPage();
    });

    document.getElementById('toggle-highlighting').addEventListener('click', () => {
      this.toggleHighlighting();
    });

    // Settings
    document.getElementById('auto-start').addEventListener('change', (e) => {
      this.settings.autoStart = e.target.checked;
      this.saveSettings();
    });

    document.getElementById('voice-enabled').addEventListener('change', (e) => {
      this.settings.voiceEnabled = e.target.checked;
      this.saveSettings();
    });

    document.getElementById('highlight-color').addEventListener('change', (e) => {
      this.settings.highlightColor = e.target.value;
      this.saveSettings();
    });

    // Response actions
    document.getElementById('clear-response').addEventListener('click', () => {
      this.clearResponse();
    });

    // Footer links
    document.getElementById('open-dashboard').addEventListener('click', () => {
      this.openDashboard();
    });

    document.getElementById('help').addEventListener('click', () => {
      this.openHelp();
    });

    document.getElementById('feedback').addEventListener('click', () => {
      this.openFeedback();
    });
  }

  async sendMessageToContentScript(message) {
    if (!this.currentTab) return;

    try {
      await chrome.tabs.sendMessage(this.currentTab.id, message);
    } catch (error) {
      console.error('Failed to send message to content script:', error);
      this.updateStatus('Error: Content script not loaded', false);
    }
  }

  async startTutorial() {
    this.showLoading('Starting tutorial...');
    
    await this.sendMessageToContentScript({
      type: 'START_TUTORIAL',
      data: {
        query: 'Please provide a comprehensive tutorial for this webpage'
      }
    });
    
    this.hideLoading();
    this.updateStatus('Tutorial started');
    window.close(); // Close popup after starting tutorial
  }

  async explainPage() {
    this.showLoading('Analyzing page...');
    
    await this.sendMessageToContentScript({
      type: 'START_TUTORIAL',
      data: {
        query: 'Please explain what this webpage is for and how to use it effectively'
      }
    });
    
    this.hideLoading();
    this.updateStatus('Explaining page');
    window.close();
  }

  async toggleHighlighting() {
    this.isHighlightingEnabled = !this.isHighlightingEnabled;
    
    const button = document.getElementById('toggle-highlighting');
    const text = document.getElementById('highlight-text');
    
    if (this.isHighlightingEnabled) {
      text.textContent = 'Disable Highlighting';
      button.classList.add('active');
      this.updateStatus('Highlighting enabled');
    } else {
      text.textContent = 'Enable Highlighting';
      button.classList.remove('active');
      this.updateStatus('Highlighting disabled');
    }
    
    await this.sendMessageToContentScript({
      type: 'TOGGLE_HIGHLIGHTING',
      enabled: this.isHighlightingEnabled
    });
  }

  async checkConnectionStatus() {
    try {
      const response = await fetch(`${this.settings.tutorAIServer}/api/ag-ui/stream`);
      if (response.ok) {
        this.updateStatus('Connected', true);
      } else {
        this.updateStatus('Server unavailable', false);
      }
    } catch (error) {
      this.updateStatus('Connection failed', false);
    }
  }

  showResponse(text) {
    const responseSection = document.getElementById('response-section');
    const responseText = document.getElementById('response-text');
    
    responseText.textContent = text;
    responseSection.style.display = 'block';
  }

  clearResponse() {
    const responseSection = document.getElementById('response-section');
    const responseText = document.getElementById('response-text');
    
    responseText.textContent = '';
    responseSection.style.display = 'none';
  }

  showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = overlay.querySelector('.loading-text');
    
    loadingText.textContent = text;
    overlay.style.display = 'flex';
  }

  hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    overlay.style.display = 'none';
  }

  openDashboard() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/dashboard`
    });
  }

  openHelp() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/help`
    });
  }

  openFeedback() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/feedback`
    });
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new TutorAIPopup();
});
