/**
 * TutorAI Background Script
 * Handles extension lifecycle, tab management, and cross-tab communication
 */

class TutorAIBackground {
  constructor() {
    this.activeTabs = new Map();
    this.settings = {};
    
    this.init();
  }

  init() {
    // Load settings on startup
    this.loadSettings();
    
    // Set up event listeners
    this.setupEventListeners();
    
    console.log('TutorAI: Background script initialized');
  }

  async loadSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['tutorAISettings'], (result) => {
        this.settings = result.tutorAISettings || {
          enabled: true,
          autoStart: false,
          voiceEnabled: false,
          highlightColor: '#3b82f6',
          tutorAIServer: 'http://localhost:3000'
        };
        resolve();
      });
    });
  }

  setupEventListeners() {
    // Extension installation/update
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details);
    });

    // Tab events
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.handleTabActivated(activeInfo);
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdated(tabId, changeInfo, tab);
    });

    chrome.tabs.onRemoved.addListener((tabId) => {
      this.handleTabRemoved(tabId);
    });

    // Message handling
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Context menu
    this.setupContextMenu();

    // Keyboard shortcuts
    chrome.commands.onCommand.addListener((command) => {
      this.handleCommand(command);
    });
  }

  handleInstallation(details) {
    if (details.reason === 'install') {
      // First installation
      this.showWelcomePage();
      this.setDefaultSettings();
    } else if (details.reason === 'update') {
      // Extension update
      this.handleUpdate(details.previousVersion);
    }
  }

  showWelcomePage() {
    chrome.tabs.create({
      url: `${this.settings.tutorAIServer}/welcome?source=extension`
    });
  }

  setDefaultSettings() {
    chrome.storage.sync.set({
      tutorAISettings: this.settings
    });
  }

  handleUpdate(previousVersion) {
    console.log(`TutorAI: Updated from version ${previousVersion}`);
    // Handle any migration logic here
  }

  handleTabActivated(activeInfo) {
    const tabId = activeInfo.tabId;
    
    // Update active tab tracking
    this.activeTabs.set(tabId, {
      id: tabId,
      active: true,
      lastActivated: Date.now()
    });

    // Auto-start if enabled
    if (this.settings.autoStart) {
      this.autoStartTutorial(tabId);
    }
  }

  async handleTabUpdated(tabId, changeInfo, tab) {
    // Only process when page is completely loaded
    if (changeInfo.status !== 'complete') return;

    // Skip chrome:// and extension pages
    if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
      return;
    }

    // Update tab info
    this.activeTabs.set(tabId, {
      id: tabId,
      url: tab.url,
      title: tab.title,
      lastUpdated: Date.now()
    });

    // Auto-start if enabled and this is the active tab
    if (this.settings.autoStart && tab.active) {
      setTimeout(() => {
        this.autoStartTutorial(tabId);
      }, 2000); // Wait 2 seconds for page to settle
    }
  }

  handleTabRemoved(tabId) {
    this.activeTabs.delete(tabId);
  }

  async handleMessage(message, sender, sendResponse) {
    switch (message.type) {
      case 'GET_SETTINGS':
        sendResponse(this.settings);
        break;

      case 'UPDATE_SETTINGS':
        await this.updateSettings(message.settings);
        sendResponse({ success: true });
        break;

      case 'START_TUTORIAL':
        await this.startTutorialOnTab(sender.tab.id, message.options);
        sendResponse({ success: true });
        break;

      case 'GET_TAB_INFO':
        const tabInfo = this.activeTabs.get(sender.tab.id);
        sendResponse(tabInfo);
        break;

      case 'REPORT_ERROR':
        this.reportError(message.error, sender.tab);
        break;

      default:
        console.warn('TutorAI: Unknown message type:', message.type);
    }
  }

  async updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    await chrome.storage.sync.set({ tutorAISettings: this.settings });
  }

  setupContextMenu() {
    chrome.contextMenus.create({
      id: 'tutorai-explain',
      title: 'Explain with TutorAI',
      contexts: ['all']
    });

    chrome.contextMenus.create({
      id: 'tutorai-tutorial',
      title: 'Start Tutorial',
      contexts: ['page']
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.handleContextMenuClick(info, tab);
    });
  }

  async handleContextMenuClick(info, tab) {
    switch (info.menuItemId) {
      case 'tutorai-explain':
        await this.explainSelection(tab.id, info);
        break;

      case 'tutorai-tutorial':
        await this.startTutorialOnTab(tab.id);
        break;
    }
  }

  async explainSelection(tabId, info) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'EXPLAIN_SELECTION',
        selectionText: info.selectionText,
        pageUrl: info.pageUrl
      });
    } catch (error) {
      console.error('Failed to explain selection:', error);
    }
  }

  async startTutorialOnTab(tabId, options = {}) {
    try {
      await chrome.tabs.sendMessage(tabId, {
        type: 'START_TUTORIAL',
        data: options
      });
    } catch (error) {
      console.error('Failed to start tutorial:', error);
      // Content script might not be loaded, try injecting it
      await this.injectContentScript(tabId);
    }
  }

  async autoStartTutorial(tabId) {
    if (!this.settings.enabled || !this.settings.autoStart) return;

    try {
      const tab = await chrome.tabs.get(tabId);
      
      // Skip certain URLs
      if (this.shouldSkipAutoStart(tab.url)) return;

      await this.startTutorialOnTab(tabId, {
        auto: true,
        query: 'Welcome! I can help you understand this webpage. Click on any element to learn more about it.'
      });
    } catch (error) {
      console.error('Auto-start tutorial failed:', error);
    }
  }

  shouldSkipAutoStart(url) {
    const skipPatterns = [
      'chrome://',
      'chrome-extension://',
      'about:',
      'moz-extension://',
      'file://',
      'localhost:3000' // Skip TutorAI's own pages
    ];

    return skipPatterns.some(pattern => url.startsWith(pattern));
  }

  async injectContentScript(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content-script.js']
      });

      await chrome.scripting.insertCSS({
        target: { tabId: tabId },
        files: ['content-styles.css']
      });
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  async handleCommand(command) {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    switch (command) {
      case 'start-tutorial':
        await this.startTutorialOnTab(tab.id);
        break;

      case 'toggle-highlighting':
        await chrome.tabs.sendMessage(tab.id, {
          type: 'TOGGLE_HIGHLIGHTING'
        });
        break;
    }
  }

  reportError(error, tab) {
    console.error('TutorAI Error:', error);
    
    // Send error to analytics if configured
    if (this.settings.tutorAIServer) {
      fetch(`${this.settings.tutorAIServer}/api/analytics/error`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: error,
          tab: {
            url: tab.url,
            title: tab.title
          },
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        })
      }).catch(err => {
        console.error('Failed to report error:', err);
      });
    }
  }
}

// Initialize background script
new TutorAIBackground();
