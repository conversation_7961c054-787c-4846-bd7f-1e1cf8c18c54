/**
 * TutorAI Content Script
 * Injects AG-UI client into webpages for real-time AI tutoring
 */

class TutorAIContentScript {
  constructor() {
    this.isActive = false;
    this.agUIClient = null;
    this.highlightedElement = null;
    this.tutorialOverlay = null;
    this.currentTutorial = null;
    
    this.init();
  }

  async init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  async setup() {
    // Check if TutorAI is enabled for this site
    const settings = await this.getSettings();
    if (!settings.enabled) return;

    // Inject AG-UI client
    await this.injectAGUIClient();
    
    // Set up message listeners
    this.setupMessageListeners();
    
    // Create tutorial overlay
    this.createTutorialOverlay();
    
    // Set up element detection
    this.setupElementDetection();
    
    console.log('TutorAI: Content script initialized');
  }

  async getSettings() {
    return new Promise((resolve) => {
      chrome.storage.sync.get(['tutorAISettings'], (result) => {
        const defaultSettings = {
          enabled: true,
          autoStart: false,
          highlightColor: '#3b82f6',
          tutorAIServer: 'http://localhost:3000'
        };
        resolve(result.tutorAISettings || defaultSettings);
      });
    });
  }

  async injectAGUIClient() {
    // Inject the AG-UI client script
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('ag-ui-client.js');
    script.onload = () => {
      // Initialize AG-UI client
      this.initializeAGUIClient();
    };
    (document.head || document.documentElement).appendChild(script);
  }

  initializeAGUIClient() {
    // Create AG-UI client instance
    window.postMessage({
      type: 'TUTORAI_INIT_AGUI',
      config: {
        baseUrl: 'http://localhost:3000',
        transport: 'sse',
        reconnectAttempts: 5,
        reconnectDelay: 1000
      }
    }, '*');
  }

  setupMessageListeners() {
    // Listen for messages from popup and background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'START_TUTORIAL':
          this.startTutorial(message.data);
          break;
        case 'EXPLAIN_ELEMENT':
          this.explainElement(message.selector, message.question);
          break;
        case 'TOGGLE_HIGHLIGHTING':
          this.toggleHighlighting(message.enabled);
          break;
        case 'GET_PAGE_INFO':
          sendResponse(this.getPageInfo());
          break;
      }
    });

    // Listen for messages from injected script
    window.addEventListener('message', (event) => {
      if (event.source !== window) return;
      
      switch (event.data.type) {
        case 'TUTORAI_AGUI_EVENT':
          this.handleAGUIEvent(event.data.event);
          break;
        case 'TUTORAI_ELEMENT_CLICKED':
          this.handleElementClick(event.data.element);
          break;
      }
    });
  }

  createTutorialOverlay() {
    // Create floating tutorial overlay
    this.tutorialOverlay = document.createElement('div');
    this.tutorialOverlay.id = 'tutorai-overlay';
    this.tutorialOverlay.innerHTML = `
      <div class="tutorai-overlay-content">
        <div class="tutorai-header">
          <img src="${chrome.runtime.getURL('icons/icon-32.png')}" alt="TutorAI">
          <span>TutorAI</span>
          <button id="tutorai-close">×</button>
        </div>
        <div class="tutorai-body">
          <div id="tutorai-status">Ready to help!</div>
          <div id="tutorai-response"></div>
          <div class="tutorai-controls">
            <button id="tutorai-start-tutorial">Start Tutorial</button>
            <button id="tutorai-explain-page">Explain Page</button>
          </div>
        </div>
      </div>
    `;
    
    // Add event listeners
    this.tutorialOverlay.querySelector('#tutorai-close').addEventListener('click', () => {
      this.hideTutorialOverlay();
    });
    
    this.tutorialOverlay.querySelector('#tutorai-start-tutorial').addEventListener('click', () => {
      this.startTutorial();
    });
    
    this.tutorialOverlay.querySelector('#tutorai-explain-page').addEventListener('click', () => {
      this.explainPage();
    });
    
    document.body.appendChild(this.tutorialOverlay);
    this.hideTutorialOverlay(); // Initially hidden
  }

  setupElementDetection() {
    // Add hover effects and click detection
    document.addEventListener('mouseover', (event) => {
      if (!this.isActive) return;
      this.highlightElement(event.target);
    });

    document.addEventListener('mouseout', (event) => {
      if (!this.isActive) return;
      this.removeHighlight(event.target);
    });

    document.addEventListener('click', (event) => {
      if (!this.isActive) return;
      event.preventDefault();
      this.handleElementClick(event.target);
    }, true);
  }

  highlightElement(element) {
    if (element === this.highlightedElement) return;
    
    // Remove previous highlight
    if (this.highlightedElement) {
      this.removeHighlight(this.highlightedElement);
    }
    
    // Add highlight
    element.classList.add('tutorai-highlighted');
    this.highlightedElement = element;
  }

  removeHighlight(element) {
    element.classList.remove('tutorai-highlighted');
    if (element === this.highlightedElement) {
      this.highlightedElement = null;
    }
  }

  handleElementClick(element) {
    const selector = this.generateSelector(element);
    const elementInfo = this.getElementInfo(element);
    
    // Send to AG-UI for explanation
    window.postMessage({
      type: 'TUTORAI_EXPLAIN_ELEMENT',
      selector: selector,
      elementInfo: elementInfo,
      pageContext: this.getPageInfo()
    }, '*');
    
    this.showTutorialOverlay();
    this.updateStatus('Analyzing element...');
  }

  generateSelector(element) {
    // Generate unique CSS selector for element
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }
    
    // Fallback to nth-child selector
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${this.generateSelector(parent)} > ${element.tagName.toLowerCase()}:nth-child(${index})`;
    }
    
    return element.tagName.toLowerCase();
  }

  getElementInfo(element) {
    return {
      tagName: element.tagName,
      text: element.textContent?.trim().substring(0, 100) || '',
      attributes: Array.from(element.attributes).reduce((acc, attr) => {
        acc[attr.name] = attr.value;
        return acc;
      }, {}),
      role: element.getAttribute('role'),
      ariaLabel: element.getAttribute('aria-label')
    };
  }

  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      domain: window.location.hostname,
      description: document.querySelector('meta[name="description"]')?.content || '',
      keywords: document.querySelector('meta[name="keywords"]')?.content || ''
    };
  }

  async startTutorial(options = {}) {
    this.showTutorialOverlay();
    this.updateStatus('Generating tutorial...');
    
    const pageInfo = this.getPageInfo();
    const domElements = this.extractDOMElements();
    
    // Send to AG-UI for tutorial generation
    window.postMessage({
      type: 'TUTORAI_GENERATE_TUTORIAL',
      pageInfo: pageInfo,
      domElements: domElements,
      userQuery: options.query || 'Please provide a tutorial for this webpage'
    }, '*');
  }

  extractDOMElements() {
    // Extract key interactive elements from the page
    const selectors = [
      'button', 'a[href]', 'input', 'select', 'textarea',
      '[role="button"]', '[role="link"]', '[role="tab"]',
      'nav', 'header', 'main', 'footer', 'aside'
    ];
    
    const elements = [];
    selectors.forEach(selector => {
      document.querySelectorAll(selector).forEach((el, index) => {
        if (index < 10) { // Limit to prevent overwhelming the AI
          elements.push({
            selector: this.generateSelector(el),
            text: el.textContent?.trim().substring(0, 50) || '',
            type: el.tagName.toLowerCase(),
            role: el.getAttribute('role') || el.tagName.toLowerCase()
          });
        }
      });
    });
    
    return elements;
  }

  explainPage() {
    this.startTutorial({ query: 'Please explain what this webpage is for and how to use it' });
  }

  handleAGUIEvent(event) {
    switch (event.type) {
      case 'TextMessageContent':
        this.updateResponse(event.delta, false);
        break;
      case 'TextMessageEnd':
        this.updateStatus('Ready');
        break;
      case 'ToolCallStart':
        if (event.toolCallName === 'highlight_element') {
          this.updateStatus('Highlighting element...');
        }
        break;
      case 'ToolCallArgs':
        try {
          const args = JSON.parse(event.delta);
          if (args.selector) {
            this.highlightElementBySelector(args.selector, args.style);
          }
        } catch (error) {
          console.error('Error parsing tool call args:', error);
        }
        break;
      case 'RunError':
        this.updateStatus(`Error: ${event.message}`);
        break;
    }
  }

  highlightElementBySelector(selector, style = {}) {
    try {
      const element = document.querySelector(selector);
      if (element) {
        // Remove previous highlights
        document.querySelectorAll('.tutorai-ai-highlighted').forEach(el => {
          el.classList.remove('tutorai-ai-highlighted');
        });
        
        // Add new highlight
        element.classList.add('tutorai-ai-highlighted');
        
        // Apply custom style if provided
        if (style.borderColor) {
          element.style.setProperty('--tutorai-highlight-color', style.borderColor);
        }
        
        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    } catch (error) {
      console.error('Error highlighting element:', error);
    }
  }

  showTutorialOverlay() {
    this.tutorialOverlay.style.display = 'block';
    this.isActive = true;
  }

  hideTutorialOverlay() {
    this.tutorialOverlay.style.display = 'none';
    this.isActive = false;
    
    // Remove all highlights
    document.querySelectorAll('.tutorai-highlighted, .tutorai-ai-highlighted').forEach(el => {
      el.classList.remove('tutorai-highlighted', 'tutorai-ai-highlighted');
    });
  }

  updateStatus(status) {
    const statusEl = this.tutorialOverlay.querySelector('#tutorai-status');
    if (statusEl) {
      statusEl.textContent = status;
    }
  }

  updateResponse(content, isComplete = true) {
    const responseEl = this.tutorialOverlay.querySelector('#tutorai-response');
    if (responseEl) {
      if (isComplete) {
        responseEl.textContent = content;
      } else {
        responseEl.textContent += content;
      }
    }
  }

  toggleHighlighting(enabled) {
    this.isActive = enabled;
    if (!enabled) {
      this.hideTutorialOverlay();
    }
  }
}

// Initialize content script
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new TutorAIContentScript();
  });
} else {
  new TutorAIContentScript();
}
