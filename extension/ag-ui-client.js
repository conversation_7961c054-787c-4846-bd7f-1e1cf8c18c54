/**
 * AG-UI Client for Browser Extension
 * Handles communication with TutorAI backend
 */

class ExtensionAGUIClient {
  constructor(config) {
    this.config = config;
    this.eventSource = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = config.reconnectAttempts || 5;
    this.reconnectDelay = config.reconnectDelay || 1000;
    
    this.setupMessageListener();
  }

  setupMessageListener() {
    // Listen for messages from content script
    window.addEventListener('message', (event) => {
      if (event.source !== window) return;
      
      switch (event.data.type) {
        case 'TUTORAI_INIT_AGUI':
          this.config = { ...this.config, ...event.data.config };
          this.connect();
          break;
        case 'TUTORAI_EXPLAIN_ELEMENT':
          this.explainElement(event.data.selector, event.data.elementInfo, event.data.pageContext);
          break;
        case 'TUTORAI_GENERATE_TUTORIAL':
          this.generateTutorial(event.data.pageInfo, event.data.domElements, event.data.userQuery);
          break;
      }
    });
  }

  async connect() {
    if (this.isConnected || this.eventSource) return;

    try {
      const url = new URL('/api/ag-ui/stream', this.config.baseUrl);
      
      this.eventSource = new EventSource(url.toString());
      
      this.eventSource.onopen = () => {
        console.log('TutorAI: Connected to AG-UI server');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.notifyContentScript('CONNECTION_STATUS', { connected: true });
      };
      
      this.eventSource.onmessage = (event) => {
        try {
          const agentEvent = JSON.parse(event.data);
          this.handleAGUIEvent(agentEvent);
        } catch (error) {
          console.error('TutorAI: Failed to parse AG-UI event:', error);
        }
      };
      
      this.eventSource.onerror = (error) => {
        console.error('TutorAI: AG-UI connection error:', error);
        this.isConnected = false;
        this.notifyContentScript('CONNECTION_STATUS', { connected: false, error: 'Connection lost' });
        this.reconnect();
      };
      
    } catch (error) {
      console.error('TutorAI: Failed to connect to AG-UI server:', error);
      this.reconnect();
    }
  }

  async reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('TutorAI: Max reconnection attempts reached');
      this.notifyContentScript('CONNECTION_STATUS', { 
        connected: false, 
        error: 'Failed to reconnect after maximum attempts' 
      });
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`TutorAI: Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.disconnect();
      this.connect();
    }, delay);
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
  }

  handleAGUIEvent(event) {
    // Forward AG-UI events to content script
    this.notifyContentScript('TUTORAI_AGUI_EVENT', { event });
  }

  notifyContentScript(type, data) {
    window.postMessage({ type, ...data }, '*');
  }

  async explainElement(selector, elementInfo, pageContext) {
    if (!this.isConnected) {
      this.notifyContentScript('TUTORAI_AGUI_EVENT', {
        event: { type: 'RunError', message: 'Not connected to TutorAI server' }
      });
      return;
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/api/ag-ui/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: 'explain_element',
          context: {
            selector: selector,
            elementInfo: elementInfo,
            pageUrl: pageContext.url,
            pageTitle: pageContext.title,
            question: `Please explain what this element does and how to interact with it.`
          },
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('TutorAI: Element explanation request sent:', result);

    } catch (error) {
      console.error('TutorAI: Failed to explain element:', error);
      this.notifyContentScript('TUTORAI_AGUI_EVENT', {
        event: { 
          type: 'RunError', 
          message: `Failed to explain element: ${error.message}` 
        }
      });
    }
  }

  async generateTutorial(pageInfo, domElements, userQuery) {
    if (!this.isConnected) {
      this.notifyContentScript('TUTORAI_AGUI_EVENT', {
        event: { type: 'RunError', message: 'Not connected to TutorAI server' }
      });
      return;
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/api/ag-ui/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: 'generate_tutorial',
          context: {
            pageUrl: pageInfo.url,
            pageTitle: pageInfo.title,
            userQuery: userQuery,
            domElements: domElements
          },
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('TutorAI: Tutorial generation request sent:', result);

    } catch (error) {
      console.error('TutorAI: Failed to generate tutorial:', error);
      this.notifyContentScript('TUTORAI_AGUI_EVENT', {
        event: { 
          type: 'RunError', 
          message: `Failed to generate tutorial: ${error.message}` 
        }
      });
    }
  }
}

// Initialize AG-UI client when script loads
let agUIClient = null;

// Listen for initialization message
window.addEventListener('message', (event) => {
  if (event.source !== window) return;
  
  if (event.data.type === 'TUTORAI_INIT_AGUI' && !agUIClient) {
    agUIClient = new ExtensionAGUIClient(event.data.config);
  }
});

console.log('TutorAI: AG-UI client script loaded');
